// lib/toast/toast.ts

export type ToastType = 'success' | 'error' | 'info';

let showToastFn: (msg: string, type?: ToastType) => void = () => {};

export const setToastFunction = (fn: typeof showToastFn) => {
  showToastFn = fn;
};

export const toast = {
  success: (msg: string) => showToastFn(msg, 'success'),
  error: (msg: string) => showToastFn(msg, 'error'),
  info: (msg: string) => showToastFn(msg, 'info'),
};
