import { Stack } from 'expo-router';
import { ToastProvider } from '../../toast/toast-provider';

export default function AccountLayout() {
  return (
    <ToastProvider>
      <Stack
        screenOptions={{
          animation: 'slide_from_right',
          headerShown: false,
        }}
      >
        <Stack.Screen name="login" />
        <Stack.Screen name="privacy-policy" />
        <Stack.Screen name="terms-and-conditions" />
        <Stack.Screen name="profile-settings" />
        <Stack.Screen name="forgot-password" />
        <Stack.Screen name="sign-up" />
      </Stack>
    </ToastProvider>
  );
}
