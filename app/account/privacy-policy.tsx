import { NavLayout } from '@/components/NavLayout';
import { Text } from '@/components/ui/text';
import { Image, View } from 'react-native';

export default function PrivacyPolicyScreen() {
  return (
    <NavLayout title="Privacy Policy" className="px-7">
      <View>
        <Text className="text-base font-urbanist">
          At Kali, we value your privacy and are committed to protecting your
          personal information. This Privacy Policy explains how we collect,
          use, and safeguard your data when you use our app. By accessing or
          using Kali, you agree to the practices described in this policy. We
          may collect information that you provide directly to us, such as your
          name, email address, and profile photo. Additionally, we collect
          information about your device and app interactions to help us improve
          our services and offer a better experience. Providing additional
          details to complete your profile is completely optional. We use your
          information primarily to create and manage your account, personalize
          your app experience, improve our app's functionality, communicate
          updates, and provide support. We do not sell your personal information
          to any third parties. Your data may be shared only with trusted
          service providers who assist in operating and improving the app, and
          only as necessary. In rare cases, we may disclose your information if
          required by law. We implement industry-standard security measures to
          protect your data. However, while we strive to keep your information
          secure, we cannot guarantee absolute security due to the nature of
          digital communications. You have the right to access, update, or
          delete your personal information at any time. If you would like to
          make any changes or request account deletion, please contact us at
          <EMAIL>. <PERSON> is intended for users who are 13 years of
          age or older. We do not knowingly collect information from children
          under 13. If we discover that we have collected personal information
          from a child, we will take appropriate action to delete it. We may
          update this Privacy Policy periodically. If significant changes are
          made, we will notify you within the app. We encourage you to review
          this policy regularly to stay informed about how we are protecting
          your information. If you have any questions or concerns regarding this
          Privacy Policy or your personal data, you can reach us at
          <EMAIL>. Last updated: April 28, 2025.
        </Text>
        <Image
          source={require('@/assets/Icons/pngs/kali-text-logo.png')}
          alt="Kalli Logo"
          className="flex mt-8"
          style={{ width: 100, height: 50 }}
          resizeMode="contain"
        />
      </View>
    </NavLayout>
  );
}
