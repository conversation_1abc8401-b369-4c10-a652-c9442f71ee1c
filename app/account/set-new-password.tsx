import React, { useState } from 'react';
import SCREENS from '@/constants/Screens';
import { Input, InputField, InputIcon, InputSlot } from '@/components/ui/input';
import { Text } from '@/components/ui/text';
import { EyeIcon, EyeOffIcon } from '@/components/ui/icon';
import { VStack } from '@/components/ui/vstack';
import { CTAButton } from '@/components/ui/primaryCTAbutton';
import { ErrorText } from '@/components/ui/errortext';
import { useRouter } from 'expo-router';
import { supabase } from '@/lib/supabase';
import { toast } from '@/toast/toast';
import { AuthScreenLayout } from '@/components/AuthScreenLayout';

export default function SetNewPasswordComponent() {
  const router = useRouter();
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSetPassword = async () => {
    setLoading(true);
    setError(null);

    if (password.trim() !== confirmPassword.trim()) {
      setError('Passwords do not match');
      setLoading(false);
      return;
    }

    try {
      const { error: updateError } = await supabase.auth.updateUser({
        password: password.trim(),
      });

      if (updateError) {
        setError(updateError.message);
        return;
      }

      toast.success('Password updated. Please login.');
      router.replace(SCREENS.LOGIN);
    } catch (err: any) {
      toast.error('Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const isFormValid =
    password.trim().length >= 6 && password === confirmPassword;

  return (
    <AuthScreenLayout>
      <VStack space="xs" className="w-full px-5">
        <Text className="text-3xl font-urbanistBold text-typography-800 text-center pb-3">
          Set New Password
        </Text>
        <Text className="text-base font-urbanist text-typography-700 text-center">
          Enter and confirm your new password
        </Text>

        <VStack space="lg" className="w-full pt-6">
          {/* Password Field */}
          <VStack space="xs" className="w-full">
            <Text className="font-urbanistSemiBold">New Password</Text>
            <Input className="w-full" size="lg">
              <InputField
                type={showNewPassword ? 'text' : 'password'}
                placeholder="Enter new password"
                className="font-urbanist text-base"
                value={password}
                onChangeText={setPassword}
                autoCapitalize="none"
                selectionColor="#1db960"
                placeholderClassName="text-xs"
              />
              <InputSlot
                className="pr-3"
                onTouchStart={() => setShowNewPassword((prev) => !prev)}
              >
                <InputIcon
                  size="xl"
                  as={showNewPassword ? EyeOffIcon : EyeIcon}
                />
              </InputSlot>
            </Input>
          </VStack>

          {/* Confirm Password Field */}
          <VStack space="xs" className="w-full">
            <Text className="font-urbanistSemiBold">Confirm Password</Text>
            <Input className="w-full" size="lg">
              <InputField
                type={showConfirmPassword ? 'text' : 'password'}
                placeholder="Re-enter new password"
                className="font-urbanist text-base"
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                autoCapitalize="none"
                selectionColor="#1db960"
                placeholderClassName="text-xs"
              />
              <InputSlot
                className="pr-3"
                onTouchStart={() => setShowConfirmPassword((prev) => !prev)}
              >
                <InputIcon
                  size="xl"
                  as={showConfirmPassword ? EyeOffIcon : EyeIcon}
                />
              </InputSlot>
            </Input>
          </VStack>

          <CTAButton
            onPress={handleSetPassword}
            loading={loading}
            isFormValid={isFormValid}
            title="Continue"
          />
          <ErrorText message={error} />
        </VStack>
      </VStack>
    </AuthScreenLayout>
  );
}
