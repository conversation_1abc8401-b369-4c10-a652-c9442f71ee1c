import { useState, useEffect } from 'react';
import { <PERSON><PERSON>View } from 'react-native';
import { useAuthUser } from '@/hooks/useAuthUser';
import { NavLayout } from '@/components/NavLayout';
import { CTAButton } from '@/components/ui/primaryCTAbutton';
import { useRouter } from 'expo-router';
import { CustomInput } from '@/components/ui/customInput';
import { supabase } from '@/lib/supabase';
import { toast } from '@/toast/toast';

export default function ProfileSettingsScreen() {
  const { user: currentUser, setUser } = useAuthUser();
  const router = useRouter();

  const [displayName, setDisplayName] = useState(
    currentUser?.user_metadata.full_name || ''
  );
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (currentUser?.user_metadata.full_name) {
      setDisplayName(currentUser.user_metadata.full_name);
    }
  }, [currentUser?.user_metadata.full_name]);

  const handleUpdateProfile = async () => {
    if (!currentUser) return;

    try {
      setLoading(true);

      const { data, error } = await supabase.auth.updateUser({
        data: {
          full_name: displayName.trim(),
        },
      });

      if (error) {
        toast.error(error.message || 'Something went wrong');
        return;
      }

      if (data?.user) {
        setUser(data.user);
        router.back();
      }
    } catch (error: any) {
      toast.error(error.message || 'Something went wrong');
    } finally {
      setLoading(false);
    }
  };

  const isFormValid = displayName.trim() !== '';

  return (
    <NavLayout title="Profile Settings" unmountOnBlur className="px-7">
      <ScrollView className="flex-1 mt-6">
        <CustomInput
          label="Display Name"
          value={displayName}
          onChangeText={setDisplayName}
          className="mb-3"
          placeholder="Enter your full name"
          type="text"
        />

        <CTAButton
          onPress={handleUpdateProfile}
          loading={loading}
          isFormValid={isFormValid}
          title="Update Profile"
        />
      </ScrollView>
    </NavLayout>
  );
}
