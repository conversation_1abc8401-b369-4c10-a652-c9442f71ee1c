import { useState, useEffect } from 'react';
import { Pressable, View } from 'react-native';
import { Text } from '@/components/ui/text';
import { Button, ButtonText, ButtonSpinner } from '@/components/ui/button';
import { NavLayout } from '@/components/NavLayout';
import { CustomInput } from '@/components/ui/customInput';
import { validateEmail } from '../../utils';
import { useRouter } from 'expo-router';
import { toast } from '../../toast/toast';
import { ErrorText } from '@/components/ui/errortext';
import { VStack } from '@/components/ui/vstack';
import { supabase } from '@/lib/supabase';
import SCREENS from '@/constants/Screens';

const COOLDOWN_DURATION = 30;

export default function ForgotPasswordScreen() {
  const [email, setEmail] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [cooldown, setCooldown] = useState(0);
  const router = useRouter();

  const isEmailValid = email && !validateEmail(email);
  const isFormValid = isEmailValid && cooldown === 0;

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (cooldown > 0) {
      timer = setTimeout(() => setCooldown((prev) => prev - 1), 1000);
    }
    return () => clearTimeout(timer);
  }, [cooldown]);

  const handleResetPassword = async () => {
    const validationError = validateEmail(email);
    if (validationError) {
      setError(validationError);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const { error: resetError } = await supabase.auth.resetPasswordForEmail(
        email,
        {
          redirectTo: `kali:/${SCREENS.SET_NEW_PASSWORD}`,
        }
      );
      if (resetError) {
        toast.error(resetError.message || 'Something went wrong. Try again!');
      } else {
        toast.success('Reset email sent successfully!');
        setCooldown(COOLDOWN_DURATION);
      }
    } catch (err) {
      toast.error('Unexpected error. Try again!');
    } finally {
      setLoading(false);
    }
  };

  return (
    <NavLayout title="Forgot Password" className="px-7">
      <View className="gap-4 pt-6">
        <Text className="text-base font-urbanist">
          Enter your email to receive password reset instructions.
        </Text>
        <VStack>
          <CustomInput
            label="Email"
            value={email}
            onChangeText={setEmail}
            placeholder="Enter your email"
            type="text"
            validate={validateEmail}
          />

          <ErrorText message={error} className="mt-2" />
        </VStack>

        <Button
          onTouchStart={handleResetPassword}
          isDisabled={!isFormValid}
          className={`mt-2 w-full ${
            isFormValid ? 'bg-primary-0' : 'bg-typography-300'
          }`}
        >
          {loading ? (
            <ButtonSpinner size="small" className="text-white" />
          ) : (
            <ButtonText
              className={`font-urbanistSemiBold ${
                isFormValid ? 'text-white' : 'text-typography-500'
              }`}
            >
              {cooldown > 0 ? `Try again in ${cooldown}s` : 'Send Reset Email'}
            </ButtonText>
          )}
        </Button>

        <Pressable className="self-center" onTouchStart={() => router.back()}>
          <Text className="text-primary-0 font-urbanistSemiBold text-sm">
            Go back to login
          </Text>
        </Pressable>
      </View>
    </NavLayout>
  );
}
