import { NavLayout } from '@/components/NavLayout';
import { Text } from '@/components/ui/text';
import { Image, View } from 'react-native';

export default function TermsAndConditions() {
  return (
    <NavLayout title="Terms & Conditions" className="px-7">
      <View className="mt-4">
        <Text className="text-base font-urbanist mb-4">
          Welcome to Kali! These Terms and Conditions ("Terms") govern your use
          of the Kali mobile application ("App") operated by us. Please read
          them carefully.
        </Text>

        <Text className="text-xl font-urbanistSemiBold mb-2">
          1. Acceptance of Terms
        </Text>
        <Text className="text-base font-urbanist mb-4">
          By accessing or using Kali, you agree to be bound by these Terms. If
          you do not agree with any part, you must not use our App.
        </Text>

        <Text className="text-xl font-urbanistSemiBold mb-2">
          2. Use of the App
        </Text>
        <Text className="text-base font-urbanist mb-4">
          You must be at least 13 years old to use Kali. You agree to use the
          App only for lawful purposes and in a way that does not violate the
          rights of others.
        </Text>

        <Text className="text-xl font-urbanistSemiBold mb-2">
          3. Account Registration
        </Text>
        <Text className="text-base font-urbanist mb-4">
          Some features may require you to create an account. You are
          responsible for maintaining the confidentiality of your account and
          password and for all activities under your account.
        </Text>

        <Text className="text-xl font-urbanistSemiBold mb-2">
          4. Content and Ownership
        </Text>
        <Text className="text-base font-urbanist mb-4">
          All content provided within the App (text, graphics, logos, etc.) is
          owned by Kali or its licensors. You may not copy, distribute, or
          modify any content without our permission.
        </Text>

        <Text className="text-xl font-urbanistSemiBold mb-2">
          5. User Conduct
        </Text>
        <Text className="text-base font-urbanist mb-4">
          You agree not to misuse the App by introducing viruses, spamming,
          attempting unauthorized access, or engaging in any activity that could
          harm the App or other users.
        </Text>

        <Text className="text-xl font-urbanistSemiBold mb-2">
          6. Termination
        </Text>
        <Text className="text-base font-urbanist mb-4">
          We reserve the right to suspend or terminate your access to the App if
          you violate these Terms or for any other reason at our sole
          discretion.
        </Text>

        <Text className="text-xl font-urbanistSemiBold mb-2">7. Privacy</Text>
        <Text className="text-base font-urbanist mb-4">
          Your privacy is important to us. Please review our Privacy Policy to
          understand how we collect, use, and protect your information.
        </Text>

        <Text className="text-xl font-urbanistSemiBold mb-2">
          8. Changes to Terms
        </Text>
        <Text className="text-base font-urbanist mb-4">
          We may update these Terms from time to time. Continued use of the App
          after changes means you accept the new Terms.
        </Text>

        <Text className="text-xl font-urbanistSemiBold mb-2">
          9. Disclaimer
        </Text>
        <Text className="text-base font-urbanist mb-4">
          The App is provided "as is" without warranties of any kind, either
          express or implied. We do not guarantee that the App will always be
          safe, secure, or error-free.
        </Text>

        <Text className="text-xl font-urbanistSemiBold mb-2">
          10. Limitation of Liability
        </Text>
        <Text className="text-base font-urbanist mb-4">
          To the fullest extent permitted by law, Kali and its affiliates shall
          not be liable for any indirect, incidental, special, or consequential
          damages arising from your use of the App.
        </Text>

        <Text className="text-xl font-urbanistSemiBold mb-2">
          11. Governing Law
        </Text>
        <Text className="text-base font-urbanist mb-4">
          These Terms are governed by the laws of [Your Country/Region], without
          regard to its conflict of law provisions.
        </Text>

        <Text className="text-xl font-urbanistSemiBold mb-2">
          12. Contact Us
        </Text>
        <Text className="text-base font-urbanist mb-4">
          If you have any questions or concerns about these Terms, feel free to
          reach out to us at [<EMAIL>].
        </Text>
        <Image
          source={require('@/assets/Icons/pngs/kali-text-logo.png')}
          alt="Kalli Logo"
          className="flex mt-8"
          style={{ width: 100, height: 50 }}
          resizeMode="contain"
        />
      </View>
    </NavLayout>
  );
}
