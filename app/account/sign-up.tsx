import React, { useState } from 'react';
import { View } from 'react-native';
import { validateEmail, validatePassword } from '../../utils';
import { CustomInput } from '@/components/ui/customInput';
import { Text } from '@/components/ui/text';
import { ErrorText } from '@/components/ui/errortext';
import { CTAButton } from '@/components/ui/primaryCTAbutton';
import { AuthScreenLayout } from '@/components/AuthScreenLayout';
import { VStack } from '@/components/ui/vstack';
import { useSignup } from '@/hooks/useSignup';

export default function SignUpScreen() {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  const { signup, loading, error, setError } = useSignup();

  const handleSignUp = async () => {
    const success = await signup(name, email, password);
    if (!success) return;
  };

  return (
    <AuthScreenLayout>
      <VStack space="xs" className="w-full px-5">
        <Text className="text-3xl font-urbanistBold text-typography-800 text-center pb-3">
          You're Almost there!
        </Text>
        <Text className="text-xl font-urbanist text-typography-700 text-center">
          Sign-up to continue
        </Text>

        <View className="gap-4 pt-6 w-full">
          <CustomInput
            label="Full Name"
            value={name}
            onChangeText={(text) => {
              setName(text);
              setError(null);
            }}
            placeholder="What should we call you?"
            type="text"
          />

          <CustomInput
            label="Email"
            value={email}
            onChangeText={(text) => {
              setEmail(text);
              setError(null);
            }}
            placeholder="<EMAIL>"
            type="text"
            validate={validateEmail}
          />

          <CustomInput
            label="Password"
            value={password}
            onChangeText={(text) => {
              setPassword(text);
              setError(null);
            }}
            placeholder="Create a strong password"
            type="password"
            maxLength={32}
            validate={validatePassword}
          />

          <ErrorText message={error} />

          <CTAButton
            onPress={handleSignUp}
            loading={loading}
            title="Create Account"
          />
        </View>
      </VStack>
    </AuthScreenLayout>
  );
}
