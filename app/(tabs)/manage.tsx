import { Layout } from '@/components/ui/layout';
import ManageGuestPrompt from '@/pages/ManageGuestPrompt';
import ManageScreen from '@/pages/ManageScreen';
import { useAuthUser } from '@/hooks/useAuthUser';
import FloatingActionMenu from '@/components/k-components/FloatingActionMenu';
import { View } from 'react-native';
import { Entypo } from '@expo/vector-icons';
import SCREENS from '@/constants/Screens';
import { useRouter } from 'expo-router';

export default function Manage() {
  const { user } = useAuthUser();
  const router = useRouter();

  if (!user) {
    return <ManageGuestPrompt />;
  }

  return (
    <View style={{ flex: 1 }}>
      {/* Scrollable content */}
      <Layout isFullscreen={false}>
        <ManageScreen />
      </Layout>

      <FloatingActionMenu
        actions={[
          {
            label: 'Create a new tournament',
            onPress: () => router.push(SCREENS.CREATE_TOURNAMENT_INTRO),
            leftIcon: <Entypo name="trophy" size={22} color="black" />,
          },
          {
            label: 'Create a new match',
            onPress: () => router.push(SCREENS.CREATE_MATCH_INTRO),
            leftIcon: <Entypo name="dribbble" size={22} color="black" />,
          },
        ]}
      />
    </View>
  );
}
