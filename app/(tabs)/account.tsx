import { View, Dimensions, FlatList, Pressable, Image } from 'react-native';
import { useCallback } from 'react';
import SCREENS from '@/constants/Screens';
import { Text } from '@/components/ui/text';
import { Button, ButtonText, ButtonSpinner } from '@/components/ui/button';
import { Icon as IconComponent, ChevronRightIcon } from '@/components/ui/icon';
import { FileTextIcon, ShieldIcon, UserIcon } from 'lucide-react-native';
import { triggerHapticFeedback } from '../../utils';
import { StatusBar as RNStatusBar } from 'react-native';
import { useFocusEffect, useRouter } from 'expo-router';
import { useAuthUser } from '@/hooks/useAuthUser';
import { Avatar, AvatarFallbackText } from '@/components/ui/avatar';
import { useLogout } from '@/hooks/useLogout';
import { useForceRerenderOnFocus } from '@/hooks/useForceRerenderOnFocus';
import { useStatusBarColor } from '@/hooks/useStatusBarColor';

const { height } = Dimensions.get('window');

const nonLoggedinSettingsItems = [
  {
    title: 'Privacy Policy',
    icon: ShieldIcon,
    path: SCREENS.PRIVACY_POLICY,
  },
  {
    title: 'Terms & Conditions',
    icon: FileTextIcon,
    path: SCREENS.TERMS_AND_CONDITIONS,
  },
];

const loggedInSettingsItems = [
  {
    title: 'Profile Settings',
    icon: UserIcon,
    path: SCREENS.PROFILE_SETTINGS,
  },
  ...nonLoggedinSettingsItems,
];

function AccountHeader() {
  const { user } = useAuthUser();
  const router = useRouter();

  if (!user) {
    return (
      <View
        style={{
          height: height * 0.35,
          borderBottomLeftRadius: 25,
          borderBottomRightRadius: 25,
          paddingHorizontal: 20,
          paddingTop: 50,
        }}
        className="bg-primary-0"
      >
        <Text className="text-3xl font-urbanistSemiBold text-white">
          Account
        </Text>
        <Text className="text-base font-urbanist text-white mt-4">
          Login or Sign up to manage your account
        </Text>
        <Button
          className="mt-6 w-40 data-[pressed]:bg-[#ffffff]"
          size="md"
          variant="solid"
          onPress={() => router.navigate(SCREENS.LOGIN)}
        >
          <ButtonText className="text-primary-500 font-urbanistSemiBold">
            Login / Sign Up
          </ButtonText>
        </Button>
      </View>
    );
  }

  return (
    <View
      style={{
        height: height * 0.35,
        borderBottomLeftRadius: 25,
        borderBottomRightRadius: 25,
        paddingHorizontal: 20,
      }}
      className="flex flex-row items-center gap-4 bg-primary-0"
    >
      <Avatar size="2xl" className="flex items-center justify-center">
        {user.user_metadata.avatar_url ? (
          <Image source={{ uri: user.user_metadata.avatar_url }} />
        ) : (
          <AvatarFallbackText className="flex items-center justify-center pt-3">
            {user?.user_metadata.full_name?.[0]?.toUpperCase() ||
              user?.email?.[0]?.toUpperCase() ||
              'U'}
          </AvatarFallbackText>
        )}
      </Avatar>
      <View className="flex flex-col">
        <Text className="text-2xl font-urbanistSemiBold text-white">
          Hey {user.user_metadata.full_name || 'Stranger'}
        </Text>
        <Text className="text-sm font-urbanist text-white mt-1">
          {user.email}
        </Text>
      </View>
    </View>
  );
}

export default function Account() {
  const { user } = useAuthUser();
  const router = useRouter();
  const { logout, loading: signOutLoading } = useLogout();
  const forceRerender = useForceRerenderOnFocus();
  useStatusBarColor();

  const settingsToRender = user
    ? loggedInSettingsItems
    : nonLoggedinSettingsItems;

  function navigateToSetting(path: string) {
    if (path) {
      router.push(path as any);
      triggerHapticFeedback();
    }
  }

  return (
    <View style={{ flex: 1 }} className="bg-white">
      <AccountHeader />

      {/* Settings Area */}
      <View className="flex px-7 py-4">
        <FlatList
          data={settingsToRender}
          keyExtractor={(item) => item.title}
          ItemSeparatorComponent={() => (
            <View className="border-b border-gray-200 my-3" />
          )}
          renderItem={({ item }) => (
            <Pressable
              className="flex-row items-center justify-between py-3"
              onPress={() => navigateToSetting(item.path)}
            >
              <View className="flex-row gap-3 items-center">
                <IconComponent
                  size="lg"
                  className="text-typography-900"
                  as={item.icon}
                />
                <Text className="text-base font-urbanistMedium text-typography-900">
                  {item.title}
                </Text>
              </View>
              <IconComponent
                size="lg"
                className="text-typography-400"
                as={ChevronRightIcon}
              />
            </Pressable>
          )}
        />
      </View>
      {user && (
        <Pressable
          className="px-8 pb-6 mt-10 flex justify-center items-center"
          disabled={signOutLoading}
          onPress={() => {
            triggerHapticFeedback();
            logout();
          }}
        >
          <Text className="text-primary-0 font-urbanistBold border-b border-primary-0">
            {signOutLoading ? 'Logging out...' : 'Logout'}
          </Text>
        </Pressable>
      )}
    </View>
  );
}
