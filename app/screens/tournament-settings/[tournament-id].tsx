import { useLocalSearchParams } from 'expo-router';
import FullscreenLoader from '@/components/k-components/FullscreenLoader';
import TournamentSettings from '@/pages/TournamentSettings';
import { useTournamentById } from '@/hooks/useTournamentById';

export default function TournamentSettingsScreen() {
  const { 'tournament-id': tournamentId } = useLocalSearchParams();
  const { tournament, loading } = useTournamentById(tournamentId as string);

  return (
    <>
      {loading ? (
        <FullscreenLoader />
      ) : (
        <TournamentSettings tournament={tournament} />
      )}
    </>
  );
}
