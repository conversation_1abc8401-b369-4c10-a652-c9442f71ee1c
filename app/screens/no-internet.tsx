import { useRouter } from 'expo-router';
import React, { useEffect, useState, useRef } from 'react';
import { Image } from 'react-native';
import { useNetworkState, getNetworkStateAsync } from 'expo-network';
import { Text } from '@/components/ui/text';
import { Center } from '@/components/ui/center';
import { VStack } from '@/components/ui/vstack';
import { Button } from '@/components/ui/button';

export default function NoInternetScreen() {
  const network = useNetworkState();
  const router = useRouter();
  const [checking, setChecking] = useState(false);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    if (network?.isConnected && network?.isInternetReachable) {
      // Add a small delay to ensure network state is stable
      debounceTimeoutRef.current = setTimeout(async () => {
        try {
          const currentState = await getNetworkStateAsync();
          if (currentState?.isConnected && currentState?.isInternetReachable) {
            if (router.canGoBack()) {
              router.back();
            }
          }
        } catch (error) {}
      }, 500);
    }

    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, [network?.isConnected, network?.isInternetReachable]);

  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  const handleRetry = async () => {
    setChecking(true);

    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      const updated = await getNetworkStateAsync();

      if (updated?.isConnected && updated?.isInternetReachable) {
        if (router.canGoBack()) {
          router.back();
        }
      }
    } catch {
    } finally {
      setChecking(false);
    }
  };

  return (
    <Center className="flex-1 px-5 bg-white">
      <VStack space="lg" className="items-center justify-center">
        <Image
          source={require('@/assets/images/lost-connection.png')}
          style={{ width: 240, height: 240, resizeMode: 'contain' }}
        />

        <Text className="text-2xl font-urbanistBold text-typography-800 text-center">
          No Internet Connection
        </Text>

        <Text className="text-base font-urbanist text-typography-600 text-center">
          Please check your connection and try again.
        </Text>

        <Button variant="link" disabled={checking} onPress={handleRetry}>
          <Text className="text-primary-0 font-urbanistSemiBold text-base border-b border-primary-0 mt-4">
            {checking ? 'Checking...' : 'Try Again'}
          </Text>
        </Button>
      </VStack>
    </Center>
  );
}
