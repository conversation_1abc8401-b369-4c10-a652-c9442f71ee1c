import {
  View,
  Text,
  TextInput,
  ScrollView,
  KeyboardAvoidingView,
  Keyboard,
} from 'react-native';
import SCREENS from '@/constants/Screens';
import { useState } from 'react';
import { useRecoilState } from 'recoil';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { SCHEMA_MAP } from '@/constants/tournamentSchemaMap';
import { CustomInput } from '@/components/ui/customInput';
import { Button, ButtonText } from '@/components/ui/button';
import { Foundation } from '@expo/vector-icons';
import { SelectGrid } from '@/components/field-components/SelectGrid';
import LocationSearch from '@/components/field-components/LocationSearch';
import DateRangePickerField from '@/components/field-components/DateRangePicker';
import UploadImageField from '@/components/field-components/UploadImageField';
import { useNavigation } from 'expo-router';
import { CommonActions } from '@react-navigation/native';
import { toast } from '@/toast/toast';

export default function FieldScreen() {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { schema: schemaParam, index } = useLocalSearchParams();
  const router = useRouter();
  const navigation = useNavigation();

  const safeSchemaParam = Array.isArray(schemaParam)
    ? schemaParam[0]
    : schemaParam;

  const currentSchema = SCHEMA_MAP[safeSchemaParam]?.schema;
  const currentAtom = SCHEMA_MAP[safeSchemaParam]?.atom;

  if (!currentSchema || !currentAtom) {
    return (
      <Text className="text-center text-red-500 text-lg">Invalid Schema</Text>
    );
  }

  const [formData, setFormData] = useRecoilState(currentAtom);

  const fieldIndex = parseInt(index as string);
  const field = currentSchema[fieldIndex];
  const fieldValue = (formData as Record<string, any>)[field.key];
  const isFieldOptional = field.required !== true;

  const isFieldValid = isFieldOptional
    ? true
    : fieldValue
    ? field.validate
      ? field.validate(fieldValue)
      : true
    : false;

  const isLastField = fieldIndex === currentSchema.length - 1;
  const isFirstField = fieldIndex === 0;

  const handleChange = (value: any) => {
    setFormData((prev: any) => ({ ...prev, [field.key]: value }));
  };

  const navigateToNextField = () => {
    router.push({
      pathname: SCREENS.FORM_CREATE,
      params: {
        index: String(fieldIndex + 1),
        schema: safeSchemaParam,
      },
    });
  };

  const handleSubmissionSuccess = (id: string) => {
    const getRedirect = SCHEMA_MAP[safeSchemaParam]?.getRedirectRoute;
    const route = getRedirect?.(id) ?? { name: SCREENS.MANAGE };

    navigation.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [route],
      })
    );
  };

  const handleFinalSubmission = async () => {
    const submit = SCHEMA_MAP[safeSchemaParam]?.submit;
    if (!submit) return toast.error('Submission not available.');

    try {
      setIsSubmitting(true);
      const { success, error, id } = await submit(formData);
      setIsSubmitting(false);

      if (!success) return toast.error(error || 'Something went wrong.');
      if (id) handleSubmissionSuccess(id);
    } catch (err: any) {
      setIsSubmitting(false);
      toast.error(err.message || 'Unexpected error occurred.');
    }
  };

  const goNext = async () => {
    Keyboard.dismiss();

    if (!isLastField && isFieldValid) return navigateToNextField();

    const submit = SCHEMA_MAP[safeSchemaParam]?.submit;
    if (!submit) return;

    await handleFinalSubmission();
  };

  const goBack = () => {
    Keyboard.dismiss();
    if (fieldIndex > 0) {
      router.back();
    }
  };

  const handleSkipField = () => {
    setFormData((prev: any) => {
      const updated = { ...prev };
      delete updated[field.key];
      return updated;
    });
    goNext();
  };

  return (
    <View className="flex-1 bg-white h-full">
      {/* Top Title Section */}
      {isFieldOptional && (
        <View className="absolute top-3 right-6">
          <Button variant="link" onPress={handleSkipField}>
            <ButtonText className="text-primary-0 font-urbanistSemiBold">
              Skip for now
            </ButtonText>
          </Button>
        </View>
      )}

      <View className="px-6 pt-14">
        <Text className="text-3xl font-urbanistBold mb-1 text-typography-800">
          {field.title}
        </Text>
        {field.description && (
          <Text className="text-base text-typography-600 font-urbanist">
            {field.description}
          </Text>
        )}
      </View>
      <KeyboardAvoidingView className="flex-1">
        {/* Middle Scrollable Inputs */}
        <View className="flex-1 px-6 mt-6">
          <ScrollView
            contentContainerStyle={{
              flexGrow: 1,
              justifyContent: 'center',
              paddingBottom: 20,
            }}
            showsVerticalScrollIndicator={false}
          >
            {/* Input Fields */}
            <View className="flex flex-col gap-0.5">
              {renderField(field, fieldValue, handleChange)}

              {field.proTip && (
                <View className="flex flex-row gap-1 items-start">
                  <Foundation name="lightbulb" size={17} color="#fb923c" />
                  <Text
                    className="text-sm text-typography-600 pt-0.5 font-urbanistSemiBold flex-1"
                    numberOfLines={2}
                    ellipsizeMode="tail"
                  >
                    {field.proTip}
                  </Text>
                </View>
              )}
            </View>
          </ScrollView>
        </View>
      </KeyboardAvoidingView>

      <View className="px-6 mb-10 pt-3">
        {/* Step Progress */}
        <View className="flex-row justify-center items-center gap-1 mb-20">
          {currentSchema.map((_: any, idx: number) => (
            <View
              key={idx}
              className={`h-2 rounded-full ${
                idx <= fieldIndex ? 'bg-primary-0' : 'bg-gray-300'
              }`}
              style={{ width: 20 }}
            />
          ))}
        </View>

        {/* Next/Back Buttons */}
        <View className="flex-row justify-between mb-4">
          {!isFirstField ? (
            <Button variant="link" onPress={goBack}>
              <ButtonText className="text-typography-900 underline underline-offset-8">
                Back
              </ButtonText>
            </Button>
          ) : (
            <View className="flex-1" />
          )}
          <Button
            isDisabled={!isFieldValid}
            onPress={() => !isSubmitting && goNext()}
            className={`${
              isFieldValid ? 'opacity-100' : 'opacity-90'
            } bg-primary-0 px-8`}
          >
            <ButtonText>
              {isSubmitting ? 'Submitting...' : isLastField ? 'Finish' : 'Next'}
            </ButtonText>
          </Button>
        </View>
      </View>
    </View>
  );
}

// --- Render Input Dynamically Based on Field Type ---
function renderField(field: any, value: any, onChange: (v: any) => void) {
  switch (field.type) {
    case 'text':
      return (
        <CustomInput
          placeholder={field.title}
          value={value || ''}
          onChangeText={onChange}
        />
      );
    case 'number':
      return (
        <TextInput
          placeholder={field.title}
          value={value ? String(value) : ''}
          onChangeText={(text) => onChange(Number(text))}
          keyboardType="numeric"
          className="border-b border-gray-300 p-3 text-lg"
        />
      );
    case 'date-range':
      return <DateRangePickerField value={value} onChange={onChange} />;
    case 'location':
      return <LocationSearch onChange={onChange} />;
    case 'file-upload':
      return (
        <UploadImageField
          value={value}
          onChange={onChange}
          bucketName="tournament-assets"
          folderPath="tournament_logos"
        />
      );
    case 'select':
      return (
        <SelectGrid
          defaultValue={value ? String(value) : ''}
          options={field.options}
          onChange={onChange}
        />
      );

    default:
      return (
        <CustomInput
          placeholder={field.title}
          value={value || ''}
          onChangeText={onChange}
        />
      );
  }
}
