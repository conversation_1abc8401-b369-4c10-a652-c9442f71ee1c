import { useFocusEffect, useLocalSearchParams, useRouter } from 'expo-router';
import { useCallback, useState } from 'react';
import { NavLayout } from '@/components/NavLayout';
import { toast } from '@/toast/toast';
import FullscreenLoader from '@/components/k-components/FullscreenLoader';
import { fetchTeamById } from '@/services/teamsService';
import TeamPage from '@/pages/Teams/TeamPage';
import { type Team } from '@/types/teams';
import { CustomMenu, type MenuOption } from '@/components/CustomMenu';
import { EditIcon, UsersIcon } from '@/components/ui/icon';
import SCREENS from '@/constants/Screens';

export default function TeamViewScreen() {
  const router = useRouter();
  const { 
    'team-id': teamId, 
    'tournament-id': tournamentId 
  } = useLocalSearchParams();
  
  const [team, setTeam] = useState<Team | null>(null);
  const [loading, setLoading] = useState(true);

  const loadTeam = useCallback(async () => {
    if (!teamId) return;
    
    setLoading(true);
    try {
      const { success, team: fetchedTeam, error } = await fetchTeamById(teamId as string);
      
      if (!success) {
        toast.error(error || 'Failed to load team details');
        router.back();
        return;
      }
      
      setTeam(fetchedTeam);
    } catch (error) {
      toast.error('Something went wrong');
      router.back();
    } finally {
      setLoading(false);
    }
  }, [teamId, router]);

  useFocusEffect(
    useCallback(() => {
      loadTeam();
    }, [loadTeam])
  );

  const handleEditSquad = () => {
    router.push({
      pathname: SCREENS.TEAM_SQUAD,
      params: {
        'team-id': teamId,
        'tournament-id': tournamentId,
        mode: 'edit',
      },
    });
  };

  const menuOptions: MenuOption[] = [
    {
      key: 'edit-squad',
      label: 'Edit Squad',
      icon: UsersIcon,
      onPress: handleEditSquad,
    },
    {
      key: 'edit-team',
      label: 'Edit Team',
      icon: EditIcon,
      onPress: () => {
        // TODO: Navigate to team edit screen
        toast.info('Team editing coming soon');
      },
      separatorBefore: true,
    },
  ];

  return (
    <NavLayout
      title={team?.name || 'Team Details'}
      isFullscreen
      right={
        team && (
          <CustomMenu
            options={menuOptions}
            placement="right top"
            offset={-20}
          />
        )
      }
    >
      {loading && <FullscreenLoader />}
      {!loading && team && (
        <TeamPage 
          team={team} 
          tournamentId={tournamentId as string}
        />
      )}
    </NavLayout>
  );
}
