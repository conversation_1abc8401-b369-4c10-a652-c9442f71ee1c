import GetStartedScreen from '@/pages/GetStartedScreen';
import { useRouter } from 'expo-router';
import { SCHEMA_MAP } from '@/constants/tournamentSchemaMap';
import { useResetRecoilState } from 'recoil';

const TOURNAMENT_SCHEMA_ID = 'tournament';

export default function CreateTournamentIntro() {
  const router = useRouter();

  const steps = [
    {
      id: 1,
      title: 'Create your Tournament',
      description:
        'Share details like sport, location, dates, and number of teams.',
      image: require('@/assets/images/T-step-1.png'),
    },
    {
      id: 2,
      title: 'Add Teams & Players',
      description:
        'Register teams, add players, and send invites to participants easily.',
      image: require('@/assets/images/T-step-2.png'),
    },
    {
      id: 3,
      title: 'Launch Your Tournament',
      description:
        'Start matches, update live scores, and track analytics effortlessly.',
      image: require('@/assets/images/T-step-3.png'),
    },
  ];
  const tournament_atom = SCHEMA_MAP[TOURNAMENT_SCHEMA_ID].atom;
  const resetTournament = useResetRecoilState(tournament_atom);
  const handleCTAClick = () => {
    resetTournament();
    router.push('/screens/create/0?schema=tournament');
  };
  return <GetStartedScreen steps={steps} onCTAClick={handleCTAClick} />;
}
