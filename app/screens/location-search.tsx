import { useRouter } from 'expo-router';
import ScreenSearch from '@/components/k-components/Search';
import { useLocationStorage } from '@/hooks/useLocationStorage';
import { useLocationSuggestions } from '@/hooks/useLocationSuggestions';

export default function LocationSearch() {
  const { saveLocation } = useLocationStorage();
  const router = useRouter();

  const { searchSuggestions, searchError, fetchSuggestions } =
    useLocationSuggestions({
      limit: 5,
      countrycodes: 'IN',
    });

  return (
    <ScreenSearch
      placeholder="Search for tournament locations..."
      storageKey="recent_searches_kalli_location"
      onSearchSelect={async (data) => {
        await saveLocation(data);
        router.back();
      }}
      title="Select Location"
      searchSuggestions={searchSuggestions}
      onSearchChange={fetchSuggestions}
      searchError={searchError}
    />
  );
}
