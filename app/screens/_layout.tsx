import { Stack } from 'expo-router';
import { ToastProvider } from '../../toast/toast-provider';

export default function ScreenLayout() {
  return (
    <ToastProvider>
      <Stack
        screenOptions={{
          animation: 'slide_from_right',
          headerShown: false,
        }}
      >
        <Stack.Screen name="location-search" />
        <Stack.Screen name="create/[index]" />
        <Stack.Screen name="tournament-view/[tournament-id]" />
        <Stack.Screen name="create-tournament-intro" />
        <Stack.Screen name="tournament-settings/[tournament-id]" />
        <Stack.Screen name="tournament/[tournament-id]/rules" />
        <Stack.Screen name="player/[player-id]/edit" />
        <Stack.Screen name="player/[player-id]/view" />
        <Stack.Screen name="view-all" />
        <Stack.Screen name="create-match-intro" />
        <Stack.Screen name="teams/create" />
        <Stack.Screen name="teams/[team-id]/squad" />
        <Stack.Screen name="teams/[team-id]/view" />
        <Stack.Screen name="no-internet" />
      </Stack>
    </ToastProvider>
  );
}
