import GetStartedScreen from '@/pages/GetStartedScreen';
import { useRouter } from 'expo-router';

export default function CreateMatchIntro() {
  const router = useRouter();

  const steps = [
    {
      id: 1,
      title: 'Set Up Your Match',
      description: 'Choose teams, set the date, time, and venue for the match.',
      image: require('@/assets/images/M-step-1.png'),
    },
    {
      id: 2,
      title: 'Define Match Format',
      description:
        'Customize rules like number of halves, innings, and tie-breakers.',
      image: require('@/assets/images/M-step-2.png'),
    },
    {
      id: 3,
      title: 'Go Live and Score',
      description:
        'Start the match, update scores live, and keep everyone informed.',
      image: require('@/assets/images/M-step-3.png'),
    },
  ];

  return <GetStartedScreen steps={steps} onCTAClick={() => {}} />;
}
