import { useLocalSearchParams } from 'expo-router';
import { Pressable } from 'react-native';
import TournamentViewScreen from '@/pages/TournamentViewScreen';
import { NavLayout } from '@/components/NavLayout';
import { Icon } from '@/components/ui/icon';
import { BoltIcon } from 'lucide-react-native';
import { useRouter } from 'expo-router';
import SCREENS from '@/constants/Screens';
import FullscreenLoader from '@/components/k-components/FullscreenLoader';
import { useTournamentById } from '@/hooks/useTournamentById';

export default function CreateMatchIntro() {
  const { 'tournament-id': tournamentId } = useLocalSearchParams();
  const { tournament, loading } = useTournamentById(tournamentId as string);
  const router = useRouter();

  const handleTournamentSettings = () => {
    router.push({
      pathname: SCREENS.TOURNAMENT_SETTINGS,
      params: { 'tournament-id': tournament?.id },
    });
  };

  return (
    <NavLayout
      title={tournament?.name || 'Tournament Details'}
      isFullscreen
      right={
        tournament && (
          <Pressable className="py-2 px-1" onPress={handleTournamentSettings}>
            <Icon as={BoltIcon} size="xl" className="text-typography-900" />
          </Pressable>
        )
      }
    >
      {loading && <FullscreenLoader />}
      {!loading && tournament && (
        <TournamentViewScreen tournament={tournament} />
      )}
    </NavLayout>
  );
}
