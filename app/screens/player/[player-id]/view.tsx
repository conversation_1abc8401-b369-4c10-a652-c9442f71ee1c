import { useFocusEffect, useLocalSearchParams, useRouter } from 'expo-router';
import { useCallback, useState } from 'react';
import { NavLayout } from '@/components/NavLayout';
import { toast } from '@/toast/toast';
import FullscreenLoader from '@/components/k-components/FullscreenLoader';
import { fetchPlayerById } from '@/services/playerService';
import PlayerPage from '@/pages/Players/PlayerPage';
import { type Player } from '@/types/player';
import { CustomMenu, type MenuOption } from '@/components/CustomMenu';
import { EditIcon, TrashIcon } from '@/components/ui/icon';
import PlayerDeleteConfirmationModal from '@/pages/Players/components/PlayerDeleteConfirmationModal';
import SCREENS from '@/constants/Screens';

export default function PlayerView() {
  const { 'player-id': playerId } = useLocalSearchParams();
  const [player, setPlayer] = useState<Player | null>(null);
  const [loading, setLoading] = useState(true);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const router = useRouter();

  useFocusEffect(
    useCallback(() => {
      setLoading(true);

      fetchPlayerById(playerId as string).then((res) => {
        if (res.success) {
          setPlayer(res.player);
        } else {
          toast.error(res.error || 'Failed to load player');
        }
        setLoading(false);
      });
    }, [playerId])
  );

  const handleEditPlayer = () => {
    router.push({
      pathname: SCREENS.PLAYER_EDIT,
      params: { 'player-id': playerId },
    });
  };

  const menuOptions: MenuOption[] = [
    {
      key: 'edit',
      label: 'Edit Player',
      icon: EditIcon,
      onPress: handleEditPlayer,
    },
    {
      key: 'delete',
      label: 'Remove Player',
      icon: TrashIcon,
      onPress: () => setShowDeleteModal(true),
      separatorBefore: true,
    },
  ];

  return (
    <>
      <NavLayout
        title={player?.name || 'Player Details'}
        isFullscreen
        right={
          player && (
            <CustomMenu
              options={menuOptions}
              placement="right top"
              offset={-20}
            />
          )
        }
      >
        {loading && <FullscreenLoader />}
        {!loading && player && <PlayerPage player={player} />}
      </NavLayout>

      <PlayerDeleteConfirmationModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        player={player}
      />
    </>
  );
}
