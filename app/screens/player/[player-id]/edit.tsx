import { useFocusEffect, useLocalSearchParams, useRouter } from 'expo-router';
import { useCallback, useState } from 'react';
import { NavLayout } from '@/components/NavLayout';
import { toast } from '@/toast/toast';
import FullscreenLoader from '@/components/k-components/FullscreenLoader';
import { fetchPlayerById } from '@/services/playerService';
import EditPlayer from '@/pages/Players/EditPlayer';
import { type Player } from '@/types/player';

export default function PlayerEdit() {
  const { 'player-id': playerId } = useLocalSearchParams();
  const [player, setPlayer] = useState<Player | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useFocusEffect(
    useCallback(() => {
      setLoading(true);

      fetchPlayerById(playerId as string).then((res) => {
        if (res.success) {
          setPlayer(res.player);
        } else {
          toast.error(res.error || 'Failed to load player');
          router.back();
        }
        setLoading(false);
      });
    }, [playerId, router])
  );

  const handleClose = () => {
    router.back();
  };

  return (
    <NavLayout
      title={player ? `Edit ${player.name}` : 'Edit Player'}
      isFullscreen
    >
      {loading && <FullscreenLoader />}
      {!loading && player && (
        <EditPlayer player={player} onClose={handleClose} />
      )}
    </NavLayout>
  );
}
