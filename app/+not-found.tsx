import { Link } from 'expo-router';
import { Image } from 'expo-image';
import { Text } from '@/components/ui/text';
import { Center } from '@/components/ui/center';
import { VStack } from '@/components/ui/vstack';

export default function NotFoundScreen() {
  return (
    <Center className="flex-1 px-5 bg-white">
      <VStack space="sm" className="items-center justify-center">
        <Image
          className="max-h-80"
          source={require('@/assets/images/page-not-found.png')}
          style={{ resizeMode: 'contain' }}
        />

        <Text className="text-2xl font-urbanistBold text-typography-800 text-center">
          Page Not Found
        </Text>

        <Text className="text-base font-urbanist text-typography-600 text-center mb-6">
          Sorry, the screen you're looking for doesn't exist.
        </Text>

        <Link href="/" asChild>
          <Text className="text-primary-0 font-urbanistSemiBold text-base border-b border-primary-0 mt-4">
            Go to Home Screen
          </Text>
        </Link>
      </VStack>
    </Center>
  );
}
