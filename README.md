# 🏆 Kali – The Sports Tournament Manager

**Kali** is a futuristic and intuitive sports tournament management app built with **Expo SDK 52**, **React Native**, and **Supabase**. It empowers organizers to effortlessly create, manage, and score tournaments while delivering a real-time experience to coaches, players, and fans.

> ⚠️ **Important:** This app is built exclusively with **Expo SDK 52**.  
> Please use the compatible version of Expo Go:  
> 👉 [https://expo.dev/go?sdkVersion=52&platform=android&device=false](https://expo.dev/go?sdkVersion=52&platform=android&device=false)

---

## ✨ Features

- ✅ Create and manage multi-sport tournaments
- ✅ Add teams and players with invite links
- ✅ Real-time match scoring, cards, and substitutions
- ✅ Player & team stats with visual insights
- ✅ Collapsible tab views for better organization
- ✅ Role-based access: Organizer, Captain, Player, Fan
- ✅ Built using **Expo Router**, **Gluestack UI**, **Tailwind**, **Supabase**, and **Recoil**

---

## 📦 Tech Stack

- **Expo SDK:** 52  
- **React Native:** 0.76  
- **Navigation:** Expo Router  
- **Backend:** Supabase  
- **UI Kit:** Gluestack UI + Tailwind  
- **State Management:** Recoil  
- **Testing:** Jest + jest-expo

---

## 🚀 Getting Started (Local Development)

### 1. 📥 Clone the repository

```bash
git clone https://github.com/your-username/kali.git
cd kali
```

### 2. 📦 Install dependencies

```bash
yarn install
```

### 3. ▶️ Run the app

Make sure you have Expo CLI installed:

```bash
yarn install -g expo-cli
```

Then use one of the following commands:

#### Android

### Run with expo-app 
```bash
yarn run android
```

### Run on emulator
```bash
yarn prebuild:android
yarn run:android
```

### Generate preview build

```bash
yarn prebuild:android
yarn build:android
```

#### Web
```bash
yarn run web
```

#### Tunnel (for testing on external devices)
```bash
yarn run start:tunnel
```

#### LAN (for local network devices)
```bash
yarn run start:lan
```

#### Clear Cache
```bash
yarn run start:clear
```

---

## 🧪 Testing

Run all tests:

```bash
yarn run test
```

---

## 🛠️ Linting

Lint your codebase:

```bash
yarn run lint
```

---

## 🗂️ Project Structure

```bash
/app                → Screens & routes (expo-router)
/components         → Reusable UI components
/constants          → Shared static values
/lib                → Utility functions and API logic
/supabase           → Supabase client and services
/types              → TypeScript types
```

---

## 📱 Expo Go (for Android SDK 52)

To run the app on a real Android device:

1. Download compatible Expo Go version  
   👉 [https://expo.dev/go?sdkVersion=52&platform=android&device=false](https://expo.dev/go?sdkVersion=52&platform=android&device=false)

2. Scan the QR code from the Expo CLI once the app is running.


---

## 🤝 Contributing

Contributions are welcome!

Please open an issue or pull request for features, bugs, or improvements.
