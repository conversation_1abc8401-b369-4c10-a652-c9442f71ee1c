import React, { useState } from 'react';
import { View, Text, Pressable } from 'react-native';
import { AsyncSelectWithSearch, type Option } from './components/k-components/AsyncSelectWithSearch';

// Test component to demonstrate the fix
export default function TestAsyncSelect() {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedValue, setSelectedValue] = useState<Option>({
    label: 'Pre-selected Item',
    value: 'pre-selected',
  });
  const [selectedValues, setSelectedValues] = useState<Option[]>([
    { label: 'Pre-selected Item 1', value: 'pre-1' },
    { label: 'Pre-selected Item 2', value: 'pre-2' },
  ]);

  // Mock options that would come from search
  const [options, setOptions] = useState<Option[]>([
    { label: 'Option A', value: 'a' },
    { label: 'Option B', value: 'b' },
    { label: 'Option C', value: 'c' },
  ]);

  const mockLoadMore = async (searchQuery: string, page: number) => {
    // Simulate search filtering - note that pre-selected items are NOT in this list
    const filteredOptions = [
      { label: 'Option A', value: 'a' },
      { label: 'Option B', value: 'b' },
      { label: 'Option C', value: 'c' },
      { label: 'Search Result 1', value: 'search-1' },
      { label: 'Search Result 2', value: 'search-2' },
    ].filter(option => 
      option.label.toLowerCase().includes(searchQuery.toLowerCase())
    );
    
    setOptions(filteredOptions);
  };

  const handleSearchChange = (query: string) => {
    // This would trigger the search and update options
    mockLoadMore(query, 1);
  };

  return (
    <View style={{ flex: 1, padding: 20 }}>
      <Text style={{ fontSize: 18, marginBottom: 20 }}>
        AsyncSelectWithSearch Test
      </Text>
      
      <Text style={{ marginBottom: 10 }}>
        Single Select Test (Pre-selected: {selectedValue?.label})
      </Text>
      <Pressable
        onPress={() => setIsOpen(true)}
        style={{
          padding: 15,
          backgroundColor: '#f0f0f0',
          borderRadius: 8,
          marginBottom: 20,
        }}
      >
        <Text>Open Single Select</Text>
      </Pressable>

      <Text style={{ marginBottom: 10 }}>
        Multi Select Test (Pre-selected: {selectedValues.length} items)
      </Text>
      <Pressable
        onPress={() => setIsOpen(true)}
        style={{
          padding: 15,
          backgroundColor: '#f0f0f0',
          borderRadius: 8,
          marginBottom: 20,
        }}
      >
        <Text>Open Multi Select</Text>
      </Pressable>

      <Text style={{ fontSize: 14, color: '#666' }}>
        Test Instructions:
        {'\n'}1. Open either select
        {'\n'}2. Notice pre-selected items are visible and checked
        {'\n'}3. Search for something (e.g., "search")
        {'\n'}4. Pre-selected items should remain visible and checked
        {'\n'}5. This demonstrates the fix for the disappearing selection issue
      </Text>

      <AsyncSelectWithSearch
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        onSelect={(selected) => {
          if (Array.isArray(selected)) {
            setSelectedValues(selected);
          } else {
            setSelectedValue(selected as Option);
          }
        }}
        selectedValue={selectedValue}
        selectedValues={selectedValues}
        multiple={false} // Change to true to test multi-select
        options={options}
        hasMore={false}
        loadMore={mockLoadMore}
        onSearchChange={handleSearchChange}
        placeholder="Search options..."
      />
    </View>
  );
}
