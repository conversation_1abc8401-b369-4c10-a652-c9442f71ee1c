{"compilerOptions": {"target": "esnext", "module": "esnext", "jsx": "react-native", "moduleResolution": "node", "strict": true, "allowJs": true, "esModuleInterop": true, "skipLibCheck": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "paths": {"@/*": ["./*"], "tailwind.config": ["./tailwind.config.js"]}}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts", "nativewind-env.d.ts"], "extends": "expo/tsconfig.base"}