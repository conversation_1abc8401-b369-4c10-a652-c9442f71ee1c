// metro.config.js
const { getDefaultConfig } = require('expo/metro-config');
const { withNativeWind } = require('nativewind/metro');

const config = getDefaultConfig(__dirname);

// ➡️ Add `.cjs` support for lucide-react-native
config.resolver.sourceExts.push('cjs');

// ➡️ Add SVG support
config.transformer = {
  babelTransformerPath: require.resolve('react-native-svg-transformer'),
  ...config.transformer,
};
config.resolver.assetExts = config.resolver.assetExts.filter(
  (ext) => ext !== 'svg'
);
config.resolver.sourceExts.push('svg');

// ➡️ Final export with NativeWind applied
module.exports = withNativeWind(config, { input: './global.css' });
