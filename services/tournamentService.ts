import { supabase } from '@/lib/supabase';
import { transformTournamentFormData } from '@/utils/tournament-utils';

interface SubmissionResult {
  success: boolean;
  error?: string;
  id?: string;
}

export async function submitTournament(
  formData: any
): Promise<SubmissionResult> {
  try {
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (!user) {
      return {
        success: false,
        error: userError?.message || 'User not authenticated',
      };
    }

    const transformedData = transformTournamentFormData(formData);

    const { error, data } = await supabase
      .from('tournaments')
      .insert([
        {
          ...transformedData,
          created_by: user.id,
        },
      ])
      .select('id')
      .single();

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true, id: data.id };
  } catch (err: any) {
    return { success: false, error: err.message || 'Unknown error occurred' };
  }
}

interface Options {
  search?: string;
  limit?: number;
  lastId?: string; // use cursor-based pagination if needed
}

interface FetchResult {
  data: any[];
  lastId: string | null;
  success: boolean;
  error?: string;
}

export async function fetchUserTournaments({
  search = '',
  limit = 10,
  page = 1,
}: {
  search?: string;
  limit?: number;
  page?: number;
}) {
  try {
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (!user) {
      return {
        success: false,
        error: userError?.message || 'User not authenticated',
        data: [],
        count: 0,
      };
    }

    let query = supabase
      .from('user_tournaments_with_status')
      .select('*', { count: 'exact' })
      .eq('created_by', user.id)
      .order('status_rank', { ascending: true })
      .order('start_date', { ascending: true })
      .range((page - 1) * limit, page * limit - 1);

    if (search) {
      query = query.ilike('name', `%${search}%`);
    }

    const { data, count, error } = await query;

    if (error) {
      return {
        success: false,
        error: error.message,
        data: [],
        count: 0,
      };
    }

    return {
      success: true,
      data: data || [],
      count: count || 0,
    };
  } catch (err: any) {
    return {
      success: false,
      error: err.message || 'Something went wrong',
      data: [],
      count: 0,
    };
  }
}

export async function fetchTournamentById(tournamentId: string): Promise<{
  success: boolean;
  data?: any;
  error?: string;
}> {
  try {
    const { data, error } = await supabase
      .from('tournaments')
      .select('*')
      .eq('id', tournamentId)
      .single();

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true, data };
  } catch (err: any) {
    return { success: false, error: err.message || 'Unknown error occurred' };
  }
}

export async function updateTournamentFields(
  tournamentId: string,
  updates: Record<string, any>
): Promise<{ success: boolean; error?: string }> {
  const { error } = await supabase
    .from('tournaments')
    .update(updates)
    .eq('id', tournamentId);

  if (error) {
    return { success: false, error: error.message };
  }

  return { success: true };
}

export async function deleteTournament(tournamentId: string) {
  const { data, error } = await supabase
    .from('tournaments')
    .delete()
    .eq('id', tournamentId);

  if (error) {
    return {
      success: false,
      error: error.message || 'Failed to delete tournament',
    };
  }

  return { success: true, data };
}
