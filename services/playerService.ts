import { supabase } from '@/lib/supabase';

export async function fetchPlayers({
  tournamentId,
  teamId,
  search = '',
  page = 1,
  limit = 10,
}: {
  tournamentId: string;
  teamId?: string;
  search?: string;
  page?: number;
  limit?: number;
}) {
  const offset = (page - 1) * limit;

  let baseQuery = supabase
    .from('players')
    .select('*', { count: 'exact' }) // total count
    .eq('tournament_id', tournamentId);

  if (teamId) {
    baseQuery = baseQuery.eq('team_id', teamId);
  }

  if (search) {
    baseQuery = baseQuery.ilike('name', `%${search}%`);
  }

  const { data, count, error } = await baseQuery
    .order('created_at', { ascending: false }) // Optional: latest first
    .range(offset, offset + limit - 1);

  if (error) {
    return { players: [], count: 0, error: error.message };
  }

  return {
    players: data,
    count: count || 0,
    error: null,
  };
}

interface CreatePlayerInput {
  tournament_id: string;
  name: string;
  team_id?: string | null;
  jersey_number?: string;
  email?: string;
  phone?: string;
}

export async function createPlayer(data: CreatePlayerInput): Promise<{
  success: boolean;
  player?: any;
  error?: string;
}> {
  const { data: insertedPlayer, error } = await supabase
    .from('players')
    .insert([
      {
        tournament_id: data.tournament_id,
        name: data.name,
        team_id: data?.team_id ?? null,
        jersey_number: data?.jersey_number || null,
        email: data?.email || null,
        phone: data?.phone || null,
      },
    ])
    .select()
    .single();

  if (error) {
    return { success: false, error: error.message };
  }

  return { success: true, player: insertedPlayer };
}

export async function fetchPlayerById(id: string): Promise<{
  success: boolean;
  player?: any;
  error?: string;
}> {
  const { data: player, error } = await supabase
    .from('players')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    return { success: false, error: error.message };
  }

  return { success: true, player };
}

interface UpdatePlayerInput {
  name?: string;
  jersey_number?: string;
  email?: string;
  phone?: string;
  team_id?: string | null;
}

export async function updatePlayer(
  id: string,
  data: UpdatePlayerInput
): Promise<{
  success: boolean;
  player?: any;
  error?: string;
}> {
  const { data: updatedPlayer, error } = await supabase
    .from('players')
    .update(data)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    return { success: false, error: error.message };
  }

  return { success: true, player: updatedPlayer };
}

export async function deletePlayer(id: string): Promise<{
  success: boolean;
  error?: string;
}> {
  const { error } = await supabase.from('players').delete().eq('id', id);

  if (error) {
    return { success: false, error: error.message };
  }

  return { success: true };
}
