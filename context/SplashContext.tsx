// context/SplashContext.tsx
import React, { createContext, useContext, useState } from 'react';

const SplashContext = createContext<{
  splashDone: boolean;
  resetSplash: () => void;
  completeSplash: () => void;
}>({
  splashDone: false,
  resetSplash: () => {},
  completeSplash: () => {},
});

export const useSplash = () => useContext(SplashContext);

export const SplashProvider = ({ children }: { children: React.ReactNode }) => {
  const [splashDone, setSplashDone] = useState(false);

  const resetSplash = () => setSplashDone(false); // to restart
  const completeSplash = () => setSplashDone(true); // to finish

  return (
    <SplashContext.Provider value={{ splashDone, resetSplash, completeSplash }}>
      {children}
    </SplashContext.Provider>
  );
};
