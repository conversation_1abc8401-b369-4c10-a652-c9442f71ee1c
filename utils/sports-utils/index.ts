import { SPORT_DETAILS } from '@/config/sportsConfig';
import { capitalizeFirst } from '..';

export function getSportLabel(sportType: string): string {
  if (!sportType || typeof sportType !== 'string' || !sportType.trim()) {
    return '';
  }

  const trimmedKey = sportType.trim();

  return (
    SPORT_DETAILS[trimmedKey as keyof typeof SPORT_DETAILS]?.label ??
    capitalizeFirst(trimmedKey)
  );
}
