import { toast } from '@/toast/toast';
import { supabase } from '@/lib/supabase';

const SESSION_ERROR_CODES = new Set([
  'refresh_token_not_found',
  'refresh_token_already_used',
  'session_expired',
  'session_not_found',
  'bad_jwt',
  'invalid_credentials',
]);

const RATE_LIMIT_ERROR_CODES = new Set([
  'over_email_send_rate_limit',
  'over_request_rate_limit',
  'over_sms_send_rate_limit',
]);

const AUTH_ERROR_MESSAGES = new Map<string, string>([
  ['refresh_token_not_found', 'Session expired. Please login again.'],
  ['refresh_token_already_used', 'Session expired. Please login again.'],
  ['session_expired', 'Session expired. Please login again.'],
  ['session_not_found', 'Session expired. Please login again.'],
  ['bad_jwt', 'Session expired. Please login again.'],
  ['invalid_credentials', 'Invalid email or password.'],

  [
    'over_email_send_rate_limit',
    'Too many emails sent. Please wait and try again.',
  ],
  ['over_request_rate_limit', 'Too many requests. Please wait and try again.'],
  ['over_sms_send_rate_limit', 'Too many SMS sent. Please wait and try again.'],

  ['email_not_confirmed', 'Please confirm your email address.'],
  ['user_not_found', 'Invalid email or password.'],
  ['email_exists', 'An account with this email already exists.'],
  ['weak_password', 'Password is too weak. Please choose a stronger password.'],
  ['email_address_invalid', 'Please enter a valid email address.'],
  ['otp_expired', 'Verification code has expired. Please request a new one.'],
]);

export const handleAuthError = async (
  error: any,
  showToast: boolean = true
): Promise<void> => {
  if (error && typeof error === 'object' && 'code' in error) {
    const errorCode = error.code;

    if (SESSION_ERROR_CODES.has(errorCode)) {
      if (showToast) {
        const message =
          AUTH_ERROR_MESSAGES.get(errorCode) ||
          'Session expired. Please login again.';
        toast.error(message);
      }
      await supabase.auth.signOut();
      return;
    }

    if (RATE_LIMIT_ERROR_CODES.has(errorCode)) {
      if (showToast) {
        const message =
          AUTH_ERROR_MESSAGES.get(errorCode) ||
          'Too many requests. Please wait and try again.';
        toast.error(message);
      }
      return;
    }

    if (showToast) {
      const message =
        AUTH_ERROR_MESSAGES.get(errorCode) ||
        'Something went wrong. Please try again.';
      toast.error(message);
    }
  } else {
    if (showToast) {
      toast.error('Something went wrong. Please try again.');
    }
  }
};

export const getAuthErrorMessage = (errorCode: string): string => {
  return (
    AUTH_ERROR_MESSAGES.get(errorCode) ||
    'Something went wrong. Please try again.'
  );
};
