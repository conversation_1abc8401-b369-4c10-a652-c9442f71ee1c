import * as Location from 'expo-location';
import { LocationData } from '@/types/location-types';

export async function fetchLocation(): Promise<LocationData> {
  try {
    const { status } = await Location.requestForegroundPermissionsAsync();

    if (status !== 'granted') {
      return {
        id: `permission-denied-${Date.now()}`,
        displayName: 'World',
        description: '',
        data: null,
      };
    }

    let position = await Location.getLastKnownPositionAsync();

    if (!position) {
      position = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Lowest,
        timeInterval: 2000,
      });
    }

    const { latitude, longitude } = position.coords;

    const geoData = await Location.reverseGeocodeAsync({ latitude, longitude });
    const place = geoData[0];

    return {
      id: place?.postalCode
        ? String(place.postalCode)
        : `generated-${Date.now()}`,
      displayName: place?.city || place?.region || place?.country || 'World',
      description: place?.country || '',
      data: place || null,
    };
  } catch (error) {
    return {
      id: `generated-fallback-${Date.now()}`,
      displayName: 'World',
      description: '',
      data: null,
    };
  }
}
