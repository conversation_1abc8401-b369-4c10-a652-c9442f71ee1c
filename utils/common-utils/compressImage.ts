import * as ImageManipulator from 'expo-image-manipulator';

/**
 * Compresses an image to 500px width and ~60% quality.
 * Ideal for logos, icons, or small uploads.
 */
export const compressImage = async (
  uri: string,
  targetWidth = 500,
  quality = 0.6
): Promise<string> => {
  const result = await ImageManipulator.manipulateAsync(
    uri,
    [{ resize: { width: targetWidth } }],
    {
      compress: quality,
      format: ImageManipulator.SaveFormat.JPEG,
    }
  );

  return result.uri;
};
