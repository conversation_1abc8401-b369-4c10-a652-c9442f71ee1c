export type TournamentInfoFormData = {
  name: string;
  logo_url: string;
};

export const getInitialTournamentInfoFormData = (
  tournament?: any
): TournamentInfoFormData => ({
  name: tournament?.name || '',
  logo_url: tournament?.logo_url || '',
});

export const validateTournamentInfoForm = (
  form: TournamentInfoFormData
): Record<string, string> => {
  const errors: Record<string, string> = {};

  if (!form.name || form.name.trim() === '') {
    errors.name = 'Tournament name is required';
  } else if (form.name.length < 2) {
    errors.name = 'Tournament name must be at least 2 characters';
  }

  return errors;
};

export const validateTournamentName = (value: string): string | null => {
  if (!value || value.trim() === '') return 'Tournament name is required';
  if (value.length < 2) return 'Tournament name must be at least 2 characters';
  return null;
};
