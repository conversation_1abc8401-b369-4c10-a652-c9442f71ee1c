import { tournamentFormSchema } from '@/constants/tournamentFormSchema';
import { tournamentForm<PERSON>tom } from '@/atoms/tournamentForm<PERSON>tom';
import { submitTournament } from '@/services/tournamentService';

export interface Schema {
  [key: string]: {
    schema: any;
    atom: any;
    submit?: (formData: any) => Promise<{
      success: boolean;
      error?: string;
      id?: string;
    }>;
    getRedirectRoute?: (id: string) => {
      name: string;
      params?: Record<string, any>;
    };
  };
}

export const SCHEMA_MAP: Schema = {
  tournament: {
    schema: tournamentFormSchema,
    atom: tournamentForm<PERSON>tom,
    submit: submitTournament,
    getRedirectRoute: (id: string) => ({
      name: 'tournament-view/[tournament-id]',
      params: { 'tournament-id': id },
    }),
  },
};
