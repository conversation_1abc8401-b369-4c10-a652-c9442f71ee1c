import { type LucideIcon } from 'lucide-react-native';
import { SPORTS } from './sportsConfig';
import {
  UsersIcon,
  Repeat2Icon,
  UsersRoundIcon,
  SwordsIcon,
} from 'lucide-react-native';
import { FieldType } from '@/components/k-components/FormField';
import RULES from '@/constants/Rules';

export type RuleEditable =
  | {
      type: 'editable'; // default
    }
  | {
      type: 'disabled';
      message: string;
    }
  | {
      type: 'warning';
      message: string;
    };

export type RuleOption = {
  label: string;
  value: string;
};

export type RuleView = {
  icon: LucideIcon;
  displayText?: (value: string | number) => string;
};

export type SportRule = {
  key: string;
  label: string;
  type: FieldType;
  required: boolean;
  default?: number | string;
  options?: RuleOption[];
  info?: string;
  view?: RuleView;
  editable?: RuleEditable;
};

export type SportType = (typeof SPORTS)[keyof typeof SPORTS];

type SportRulesConfig = Record<SportType, SportRule[]>;

export const sportRulesConfig: SportRulesConfig = {
  [SPORTS.FIELD_HOCKEY]: [
    {
      key: RULES.TEAM_SIZE,
      label: 'Number of Players per Team',
      type: FieldType.NUMBER,
      required: true,
      info: 'Hockey matches typically feature 11 players per team.',
      view: {
        icon: UsersIcon,
        displayText: (val) => `${val} players per team`,
      },
    },
    {
      key: 'gender',
      label: 'Gender Category',
      type: FieldType.SELECT,
      required: false,
      options: [
        { label: 'Male', value: 'male' },
        { label: 'Female', value: 'female' },
        { label: 'Mixed', value: 'mixed' },
      ],
      info: 'Select the appropriate gender category for teams.',
      view: {
        icon: UsersRoundIcon,
        displayText: (val) => `Category - ${val}`,
      },
    },
  ],

  [SPORTS.PICKLEBALL]: [
    {
      key: RULES.GAME_FORMAT,
      label: 'Match Format',
      type: FieldType.RADIO,
      required: true,
      options: [
        { label: 'Singles', value: 'singles' },
        { label: 'Doubles', value: 'doubles' },
      ],
      info: 'Choose between singles (1v1) or doubles (2v2) format.',
      view: {
        icon: SwordsIcon,
        displayText: (val) => `Format - ${val}`,
      },
    },
    {
      key: 'gender',
      label: 'Gender Category',
      type: FieldType.SELECT,
      required: false,
      options: [
        { label: 'Male', value: 'male' },
        { label: 'Female', value: 'female' },
        { label: 'Mixed', value: 'mixed' },
      ],
      info: 'Specify the gender category participating.',
      view: {
        icon: UsersRoundIcon,
        displayText: (val) => `Category - ${val}`,
      },
    },
  ],
};
