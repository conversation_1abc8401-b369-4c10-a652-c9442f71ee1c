import {
  FieldType,
  type FieldConfig,
} from '@/components/k-components/FormField';
import {
  checkIfTeamNameExists,
  checkIfShortNameExists,
} from '@/services/teamsService';

// Validator for short name
const validateShortName = (value: string): string | null => {
  if (!value || value.trim() === '') return 'Short name is required';
  if (value.length > 4) return 'Short name must be 4 characters or less';
  return null;
};

// Validator for team name
const validateTeamName = (value: string): string | null => {
  if (!value || value.trim() === '') return 'Team name is required';
  if (value.length < 2) return 'Team name must be at least 2 characters';
  return null;
};

const createTeamNameAsyncValidator = (
  tournamentId: string,
  initialTeamId?: string
) => {
  return async (value: string): Promise<string | null> => {
    if (!value || value.trim() === '' || value.length < 2) {
      return null;
    }

    try {
      const exists = await checkIfTeamNameExists(
        tournamentId,
        value,
        initialTeamId
      );
      return exists ? 'Team name already exists' : null;
    } catch (error) {
      return 'Error checking team name';
    }
  };
};

const createShortNameAsyncValidator = (
  tournamentId: string,
  initialTeamId?: string
) => {
  return async (value: string): Promise<string | null> => {
    if (!value || value.trim() === '') {
      return null;
    }

    try {
      const exists = await checkIfShortNameExists(
        tournamentId,
        value,
        initialTeamId
      );
      return exists ? 'Short name already exists' : null;
    } catch (error) {
      return 'Error checking short name';
    }
  };
};

export const createTeamFormFields = (
  tournamentId: string,
  initialTeamId?: string
): FieldConfig[] => [
  {
    key: 'name',
    type: FieldType.TEXT,
    label: 'Team Name',
    required: true,
    validators: [
      validateTeamName,
      createTeamNameAsyncValidator(tournamentId, initialTeamId),
    ],
    info: 'Enter the full name of your team',
  },
  {
    key: 'short_name',
    type: FieldType.TEXT,
    label: 'Team Short Name',
    required: true,
    validators: [
      validateShortName,
      createShortNameAsyncValidator(tournamentId, initialTeamId),
    ],
    info: 'Maximum 4 characters (e.g., "BARCA" for Barcelona)',
  },
  {
    key: 'logo_url',
    type: FieldType.UPLOAD_IMAGE,
    label: 'Team Logo',
    required: false,
    bucketName: 'tournament-assets',
    folderPath: 'team_logos',
    info: 'Upload your team logo (JPG/PNG, max 150KB)',
  },
  {
    key: 'jersey_color',
    type: FieldType.COLOR_PICKER,
    label: 'Jersey Color',
    required: false,
    info: "Choose your team's primary jersey color",
  },
];
