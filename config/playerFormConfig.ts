import {
  FieldType,
  type FieldConfig,
} from '@/components/k-components/FormField';
import { validateEmail } from '@/utils';

export const playerFormFields: FieldConfig[] = [
  {
    key: 'name',
    type: FieldType.TEXT,
    label: 'Player Name',
    required: true,
  },
  {
    key: 'jersey_number',
    type: FieldType.NUMBER,
    label: 'Jersey Number',
    required: false,
  },
  {
    key: 'email',
    type: FieldType.TEXT,
    label: 'Email',
    required: false,
    validators: [validateEmail],
  },
];
