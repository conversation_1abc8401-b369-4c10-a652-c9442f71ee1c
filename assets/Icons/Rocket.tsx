import React from "react";
import { Svg, Path } from "react-native-svg";

const SvgComponent = () => {
  return (
    <Svg width="25" height="24" viewBox="0 0 25 24" fill="none">
      <Path
        d="M13.3902 7.43126C14.3665 6.45495 15.9494 6.45495 16.9257 7.43126C17.902 8.40757 17.902 9.99048 16.9257 10.9668C15.9494 11.9431 14.3665 11.9431 13.3902 10.9668C12.4139 9.99048 12.4139 8.40757 13.3902 7.43126ZM15.8651 8.49192C15.4745 8.10139 14.8414 8.10139 14.4508 8.49192C14.0603 8.88244 14.0603 9.51561 14.4508 9.90613C14.8414 10.2967 15.4745 10.2967 15.8651 9.90613C16.2556 9.51561 16.2556 8.88244 15.8651 8.49192ZM21.8416 4.32265C21.5728 3.45785 20.8955 2.78081 20.0306 2.51238L19.3685 2.30692C16.975 1.56409 14.367 2.20851 12.5949 3.98062L11.599 4.9765C10.2292 3.94786 8.27617 4.0567 7.02985 5.30302L5.78732 6.54555C5.49443 6.83844 5.49443 7.31331 5.78732 7.60621L7.3783 9.19718L7.19849 9.37699C6.51507 10.0604 6.51507 11.1684 7.19849 11.8519L7.69383 12.3472L6.29827 13.1432C6.09513 13.259 5.95766 13.463 5.92653 13.6948C5.89539 13.9266 5.97415 14.1596 6.13952 14.325L10.0286 18.2141C10.1939 18.3793 10.4267 18.4581 10.6583 18.4271C10.89 18.3962 11.0939 18.259 11.21 18.0562L12.0079 16.6613L12.5051 17.1584C13.1885 17.8419 14.2965 17.8419 14.9799 17.1584L15.1567 16.9816L16.7463 18.5712C17.0392 18.8641 17.5141 18.8641 17.807 18.5712L19.0495 17.3287C20.2953 16.0829 20.4046 14.1308 19.3772 12.7612L20.3751 11.7632C22.1479 9.99043 22.7921 7.38111 22.048 4.98696L21.8416 4.32265ZM19.586 3.94498C19.9791 4.06699 20.287 4.37473 20.4092 4.76783L20.6156 5.43213C21.1944 7.29426 20.6933 9.32372 19.3145 10.7026L13.9193 16.0978C13.8216 16.1954 13.6633 16.1954 13.5657 16.0978L8.25915 10.7912C8.16152 10.6936 8.16152 10.5353 8.25915 10.4377L13.6555 5.04128C15.0338 3.66297 17.0623 3.16176 18.9239 3.73951L19.586 3.94498ZM18.2971 13.8413C18.7496 14.607 18.6469 15.61 17.9888 16.2681L17.2766 16.9803L16.2174 15.921L18.2971 13.8413ZM8.09051 6.36368C8.74902 5.70516 9.75305 5.60275 10.519 6.05644L8.43896 8.13652L7.37831 7.07588L8.09051 6.36368ZM10.9086 15.562L10.3953 16.4594L7.8951 13.9592L8.79348 13.4469L10.9086 15.562ZM7.02287 18.3947C7.31577 18.1018 7.31577 17.6269 7.02287 17.334C6.72998 17.0411 6.25511 17.0411 5.96221 17.334L3.48734 19.8089C3.19445 20.1018 3.19445 20.5767 3.48734 20.8696C3.78023 21.1625 4.25511 21.1625 4.548 20.8696L7.02287 18.3947ZM5.07829 15.3895C5.37119 15.6824 5.37119 16.1573 5.07829 16.4501L4.01763 17.5108C3.72474 17.8037 3.24987 17.8037 2.95697 17.5108C2.66408 17.2179 2.66408 16.743 2.95697 16.4501L4.01763 15.3895C4.31053 15.0966 4.7854 15.0966 5.07829 15.3895ZM8.96539 20.3413C9.25829 20.0484 9.25829 19.5736 8.9654 19.2807C8.67251 18.9878 8.19764 18.9878 7.90474 19.2807L6.84614 20.3392C6.55324 20.6321 6.55324 21.107 6.84612 21.3999C7.13901 21.6928 7.61389 21.6928 7.90678 21.3999L8.96539 20.3413Z"
        fill="#FAFAFA"
      />
    </Svg>
  );
};

export default SvgComponent;
