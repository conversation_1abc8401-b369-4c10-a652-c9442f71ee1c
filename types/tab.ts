import { ReactNode } from 'react';

export interface TabItem<T = any> {
  id: string;
  name: string;
  render: (data: T) => ReactNode;
}

export interface TabBarConfig {
  scrollEnabled?: boolean;
  minWidth?: number;
  paddingVertical?: number;
  paddingHorizontal?: number;
  fontFamily?: string;
  fontWeight?: string;
  textTransform?: 'none' | 'capitalize' | 'uppercase' | 'lowercase';
  textAlign?: 'left' | 'center' | 'right';
  margin?: number;
  tabStyle?: {
    minWidth?: number;
    alignItems?: 'flex-start' | 'center' | 'flex-end';
    justifyContent?: 'flex-start' | 'center' | 'flex-end';
  };
  contentContainerStyle?: {
    backgroundColor?: string;
  };
}

export interface CollapsibleTabViewProps<T = any> {
  data: T;
  tabs: TabItem<T>[];
  tabBarConfig?: TabBarConfig;
  lazy?: boolean;
  renderHeader?: () => ReactNode;
  headerHeight?: number;
  initialTabName?: string;
  onTabChange?: (params: { tabName: string }) => void;
  showsVerticalScrollIndicator?: boolean;
  tabFilter?: (tab: TabItem<T>, data: T) => boolean;
}
