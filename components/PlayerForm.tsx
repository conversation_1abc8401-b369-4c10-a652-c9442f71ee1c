import React, { useState } from 'react';
import { ScrollView, Text, View } from 'react-native';
import { VStack } from '@/components/ui/vstack';
import FormField, { FieldType } from '@/components/k-components/FormField';
import { CTAButton } from '@/components/ui/primaryCTAbutton';
import {
  Accordion,
  AccordionItem,
  AccordionHeader,
  AccordionTrigger,
  AccordionIcon,
  AccordionContent,
} from '@/components/ui/accordion';
import { PlusIcon, MinusIcon } from 'lucide-react-native';
import { ErrorText } from '@/components/ui/errortext';
import LogoImage from '@/components/k-components/LogoImage';
import { playerFormFields } from '@/config/playerFormConfig';
import { type PlayerFormData } from '@/types/player';
import {
  getInitialFormData,
  validatePlayerForm,
} from '@/pages/Players/utils/playerFormUtils';

interface PlayerFormProps {
  initialData?: any;
  onSubmit: (formData: PlayerFormData) => Promise<void>;
  submitButtonText: string;
  isLoading?: boolean;
}

const PlayerForm: React.FC<PlayerFormProps> = ({
  initialData,
  onSubmit,
  submitButtonText,
  isLoading = false,
}) => {
  const [form, setForm] = useState<PlayerFormData>(() =>
    getInitialFormData(initialData)
  );
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [apiError, setApiError] = useState<string | null>(null);

  const handleChange = (key: string, value: any) => {
    setForm((prev) => ({ ...prev, [key]: value }));
    setErrors((prev) => ({ ...prev, [key]: '' }));
    setApiError(null);
  };

  const handleSubmit = async () => {
    const validationErrors = validatePlayerForm(form);

    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    try {
      await onSubmit(form);
    } catch (error: any) {
      setApiError(error.message || 'An error occurred');
    }
  };

  // Separate required and optional fields
  const requiredFields = playerFormFields.filter((field) => field.required);
  const optionalFields = playerFormFields.filter((field) => !field.required);

  const renderFormField = (field: any) => (
    <FormField
      key={field.key}
      type={field.type as FieldType}
      keyName={field.key}
      field={field}
      value={form[field.key as keyof PlayerFormData]}
      error={errors[field.key]}
      onChange={handleChange}
    />
  );

  return (
    <ScrollView showsVerticalScrollIndicator={false}>
      <VStack className="px-4 py-4 space-y-4 w-full">
        <View className="flex items-center pt-3">
          <LogoImage
            width={80}
            height={80}
            borderRadius={100}
            fallbackText={form.name}
            fallBacktextClassName={'text-2xl font-urbanistBold self-center'}
          />
        </View>

        {/* Required Fields */}
        {requiredFields.map(renderFormField)}

        {/* Optional Fields in Accordion */}
        {optionalFields.length > 0 && (
          <Accordion
            type="single"
            className="border-none !p-0 !m-0 rounded-md overflow-hidden shadow-none"
          >
            <AccordionItem value="additional">
              <AccordionHeader>
                <AccordionTrigger className="px-0">
                  {({ isExpanded }) => (
                    <>
                      <Text className="font-urbanistBold text-typography-800">
                        Additional Details
                      </Text>
                      <AccordionIcon as={isExpanded ? MinusIcon : PlusIcon} />
                    </>
                  )}
                </AccordionTrigger>
              </AccordionHeader>
              <AccordionContent className="pt-2 px-0">
                {optionalFields.map(renderFormField)}
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        )}

        <CTAButton
          title={submitButtonText}
          onPress={handleSubmit}
          loading={isLoading}
        />
        <ErrorText message={apiError} />
      </VStack>
    </ScrollView>
  );
};

export default PlayerForm;
