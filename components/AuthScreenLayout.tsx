// components/AuthScreenLayout.tsx

import React from 'react';
import { View, Image, StyleSheet, ViewStyle } from 'react-native';
import { Layout } from './ui/layout';
import { Center } from '@/components/ui/center';

interface AuthScreenLayoutProps {
  children: React.ReactNode;
  logo?: boolean;
  style?: ViewStyle;
}

export function AuthScreenLayout({
  children,
  logo = true,
  style,
}: AuthScreenLayoutProps) {
  return (
    //@ts-ignore
    <Layout style={[styles.container, style]}>
      {logo && (
        <Image
          source={require('@/assets/Icons/pngs/kali-text-logo.png')}
          alt="Kalli Logo"
          resizeMode="contain"
          style={styles.logo}
        />
      )}
      <View style={styles.contentContainer}>
        <Center className="w-full">{children}</Center>
      </View>
    </Layout>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  logo: {
    width: 120,
    height: 40,
    marginTop: 20,
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
  },
});
