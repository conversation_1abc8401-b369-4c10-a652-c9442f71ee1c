import React from 'react';
import { useForceRerenderOnFocus } from '@/hooks/useForceRerenderOnFocus';

const ForceRenderLayout: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const forceRerender = useForceRerenderOnFocus();
  return <>{children}</>;
};

interface RenderLayoutProps {
  unmountOnBlur?: boolean;
  children: React.ReactNode;
}

const RenderLayout: React.FC<RenderLayoutProps> = ({
  unmountOnBlur = false,
  children,
}) => {
  return unmountOnBlur ? (
    <ForceRenderLayout>{children}</ForceRenderLayout>
  ) : (
    <>{children}</>
  );
};

export default RenderLayout;
