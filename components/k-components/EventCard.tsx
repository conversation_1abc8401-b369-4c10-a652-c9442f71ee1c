import { View, Text, Image, Pressable } from 'react-native';
import { useRouter } from 'expo-router';
import LogoImage from './LogoImage';

export interface EventCardProps {
  name: string;
  venue: string;
  logo?: string;
  type: string;
  status: 'live' | 'upcoming' | 'completed';
  routeOnPress: string | { pathname: string; params?: Record<string, string> };
}

const statusColors = {
  live: 'bg-green-500',
  upcoming: 'bg-blue-500',
  completed: 'bg-gray-400',
};

export default function EventCard({
  name,
  venue,
  type,
  logo,
  status,
  routeOnPress,
}: EventCardProps) {
  const getInitial = (text: string) =>
    text ? text.charAt(0).toUpperCase() : '?';
  const router = useRouter();

  const onPress = () => {
    if (typeof routeOnPress === 'string') {
      router.push(routeOnPress);
    } else {
      router.push(routeOnPress);
    }
  };

  return (
    <Pressable
      onPress={onPress}
      className="flex-row items-start bg-white rounded-xl shadow-sm mb-4 p-4 border border-gray-200"
    >
      {/* Logo or Letter Fallback */}
      <LogoImage height={58} width={58} logoUrl={logo} fallbackText={name} />

      {/* Info */}
      <View className="ml-4 flex-1">
        <Text
          className="text-lg font-urbanistBold text-typography-900 mr-4"
          numberOfLines={1}
        >
          {name}
        </Text>
        <View className="flex flex-row items-center gap-1">
          <Text className="text-sm text-typography-600 font-urbanistMedium">
            {type.charAt(0).toUpperCase() + type.slice(1)}
          </Text>
          <View className="w-1 h-1 rounded-full bg-typography-600 mx-1 mt-1" />
          <Text
            className="text-sm text-typography-600 font-urbanistMedium"
            numberOfLines={1}
          >
            {venue}
          </Text>
        </View>
      </View>

      {/* Status Badge */}
      <View className={`px-2 py-1 rounded-md ${statusColors[status]}`}>
        <Text className="text-white text-xs font-bold">
          {String(status).toUpperCase()}
        </Text>
      </View>
    </Pressable>
  );
}
