import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  Pressable,
  ScrollView,
  Keyboard,
  TouchableWithoutFeedback,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { NavLayout } from '@/components/NavLayout';
import SearchBar from './SearchBar';
import { Icon } from '../ui/icon';
import { HistoryIcon } from 'lucide-react-native';

interface SearchSuggestion {
  id: string;
  displayName: string;
  description?: string;
  data: any;
}

interface ScreenSearchProps {
  placeholder?: string;
  onSearchSelect?: (data: SearchSuggestion) => void | Promise<void>;
  storageKey: string;
  title?: string;
  searchSuggestions?: SearchSuggestion[];
  onSearchChange?: (text: string) => void;
  searchError?: string | null;
}

export default function ScreenSearch({
  placeholder = 'Search...',
  onSearchSelect,
  storageKey,
  title = 'Search',
  searchSuggestions,
  onSearchChange = () => {},
  searchError = null,
}: ScreenSearchProps) {
  const [searchText, setSearchText] = useState('');
  const [recentSearches, setRecentSearches] = useState<SearchSuggestion[]>([]);
  const [showSuggestions, setShowSuggestions] = useState<boolean>(false);

  useEffect(() => {
    loadRecentSearches();
  }, []);

  const loadRecentSearches = async () => {
    try {
      const stored = await AsyncStorage.getItem(storageKey);
      if (stored) {
        setRecentSearches(JSON.parse(stored));
      }
    } catch (error) {
      console.log('Error loading recent searches', error);
    }
  };

  const saveRecentSearches = async (searches: SearchSuggestion[]) => {
    try {
      const slicedSearches = searches.slice(0, 5);
      await AsyncStorage.setItem(storageKey, JSON.stringify(slicedSearches));
      setRecentSearches(slicedSearches);
    } catch (error) {
      console.log('Error saving recent searches', error);
    }
  };

  const handleRecentSearchPress = async (data: SearchSuggestion) => {
    if (onSearchSelect) {
      await onSearchSelect(data);
    }
    setShowSuggestions(false);
    Keyboard.dismiss();
  };

  const handleDebouncedSearch = (debouncedText: string) => {
    setSearchText(debouncedText);
    if (onSearchChange && debouncedText.trim().length >= 3) {
      onSearchChange(debouncedText);
    }
    if (debouncedText.trim().length > 0) {
      setShowSuggestions(true);
    } else {
      setShowSuggestions(false);
    }
  };

  const handleSuggestionPress = async (suggestion: SearchSuggestion) => {
    const updated = [
      suggestion,
      ...recentSearches.filter((item) => item.id !== suggestion.id),
    ].slice(0, 5);
    await saveRecentSearches(updated);
    setSearchText(suggestion.displayName);

    if (onSearchSelect) {
      await onSearchSelect(suggestion);
    }

    setShowSuggestions(false);
    Keyboard.dismiss();
  };

  const handleOutsidePress = () => {
    Keyboard.dismiss();
    setShowSuggestions(false);
  };

  const shouldShowSuggestions =
    showSuggestions && ((searchSuggestions?.length ?? 0) > 0 || !!searchError);

  return (
    <NavLayout title={title} className="px-7">
      <TouchableWithoutFeedback onPress={handleOutsidePress}>
        <View className="flex-1 bg-white mt-4">
          {/* Search Input */}

          <SearchBar
            value={searchText}
            onDebouncedChange={handleDebouncedSearch}
            placeholder={placeholder}
          />

          {/* Search Suggestions */}
          {shouldShowSuggestions && searchText.length > 0 && (
            <View className="bg-white absolute top-12 left-0 right-5 z-10 rounded-lg shadow-lg h-52 border border-gray-200 w-full">
              <ScrollView
                keyboardShouldPersistTaps="handled"
                contentContainerStyle={{ flexGrow: 1 }}
              >
                {searchError ? (
                  <View className="flex-1 justify-center items-center">
                    <Text className="text-typography-600 font-urbanistSemiBold">
                      {searchError}
                    </Text>
                  </View>
                ) : (
                  (searchSuggestions ?? []).map((suggestion) => (
                    <Pressable
                      key={suggestion.id}
                      onTouchEndCapture={() =>
                        handleSuggestionPress(suggestion)
                      }
                      className="p-4 border-b border-gray-100 z-40"
                    >
                      <Text className="text-typography-600 font-urbanistSemiBold">
                        {suggestion?.displayName}
                      </Text>
                      {suggestion?.description && (
                        <Text className="text-typography-500 text-xs font-urbanist">
                          {suggestion?.description}
                        </Text>
                      )}
                    </Pressable>
                  ))
                )}
              </ScrollView>
            </View>
          )}

          {/* Recently Searched */}
          {recentSearches.length > 0 && (
            <View>
              <Text className="text-md mb-3 font-urbanistBold text-primary-0">
                Recently Searched
              </Text>

              <View className="flex-row flex-wrap">
                {recentSearches.map((item, index) => (
                  <Pressable
                    key={index}
                    onTouchStart={() => handleRecentSearchPress(item)}
                    className="border border-gray-200 py-2 px-3 items-center rounded-full mr-3 mb-3 max-w-[180px]"
                  >
                    <View className="flex flex-row gap-1 items-center px-3">
                      <Icon
                        as={HistoryIcon}
                        size="lg"
                        className="text-typography-600"
                      />
                      <Text
                        className="text-typography-600 font-urbanistSemiBold"
                        numberOfLines={1}
                        ellipsizeMode="tail"
                      >
                        {item?.displayName}
                      </Text>
                    </View>
                  </Pressable>
                ))}
              </View>
            </View>
          )}
        </View>
      </TouchableWithoutFeedback>
    </NavLayout>
  );
}
