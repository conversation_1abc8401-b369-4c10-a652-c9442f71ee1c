import React from 'react';
import { View, ActivityIndicator, ViewStyle } from 'react-native';

interface FullscreenLoaderProps {
  position?: 'center' | 'top';
  backgroundColor?: string;
}

export default function FullscreenLoader({
  position = 'center',
  backgroundColor = '#ffffff',
}: FullscreenLoaderProps) {
  const justifyStyle: ViewStyle = {
    justifyContent: position === 'top' ? 'flex-start' : 'center',
  };

  return (
    <View
      className="flex-1 items-center px-4"
      style={[
        { backgroundColor, paddingTop: position === 'top' ? 40 : 0 },
        justifyStyle,
      ]}
    >
      <ActivityIndicator size="large" color="#1DB960" />
    </View>
  );
}
