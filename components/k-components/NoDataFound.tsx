import React from 'react';
import { View } from 'react-native';
import { Text } from '@/components/ui/text';

const NoDataFound = ({
  title,
  subtitle,
  action,
}: {
  title: string;
  subtitle: string;
  action?: React.ReactNode;
}) => (
  <View className="flex flex-col items-center justify-center min-h-96 px-4">
    <Text className="text-4xl font-urbanistExtraBold text-typography-300 text-center">
      {title}
    </Text>
    <Text className="text-sm font-urbanistMedium text-typography-500 mt-1 mb-6 text-center">
      {subtitle}
    </Text>

    {action ? <View className="mt-2">{action}</View> : null}
  </View>
);

export default NoDataFound;
