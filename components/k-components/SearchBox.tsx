import { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  Pressable,
  Keyboard,
  TouchableWithoutFeedback,
} from 'react-native';
import SearchBar from './SearchBar';

interface SearchSuggestion {
  id: string;
  displayName: string;
  description?: string;
  data: any;
}

interface SearchBoxProps {
  placeholder?: string;
  onSearchChange?: (text: string) => void;
  onSuggestionSelect?: (data: SearchSuggestion) => void;
  searchSuggestions?: SearchSuggestion[];
  searchError?: string | null;
}

export default function SearchBox({
  placeholder = 'Search...',
  onSearchChange = () => {},
  onSuggestionSelect,
  searchSuggestions = [],
  searchError = null,
}: SearchBoxProps) {
  const [searchText, setSearchText] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isSelectingSuggestion, setIsSelectingSuggestion] = useState(false);

  const handleDebouncedSearch = (debouncedText: string) => {
    setSearchText(debouncedText);
    if (debouncedText.trim().length >= 3) {
      onSearchChange(debouncedText);
    }

    if (isSelectingSuggestion) {
      setIsSelectingSuggestion(false);
      return;
    }

    if (debouncedText.trim().length > 0) {
      setShowSuggestions(true);
    } else {
      setShowSuggestions(false);
    }
  };

  const handleSuggestionPress = (suggestion: SearchSuggestion) => {
    if (onSuggestionSelect) {
      onSuggestionSelect(suggestion);
    }
    setIsSelectingSuggestion(true);
    setSearchText(suggestion.displayName);
    setShowSuggestions(false);
    Keyboard.dismiss();
  };

  const handleOutsidePress = () => {
    Keyboard.dismiss();
    setShowSuggestions(false);
  };

  const shouldShowSuggestions =
    showSuggestions && (searchSuggestions.length > 0 || !!searchError);

  return (
    <TouchableWithoutFeedback onPress={handleOutsidePress}>
      <View className="w-full z-50">
        {/* Search Input */}
        <SearchBar
          value={searchText}
          onDebouncedChange={handleDebouncedSearch}
          placeholder={placeholder}
          delay={400}
        />

        {/* Suggestions Dropdown */}
        {shouldShowSuggestions && (
          <View className="bg-white border border-gray-200 rounded-lg shadow-md max-h-60 z-50">
            <ScrollView
              keyboardShouldPersistTaps="handled"
              contentContainerStyle={{ flexGrow: 1 }}
            >
              {searchError ? (
                <View className="flex-1 justify-center items-center py-4">
                  <Text className="text-typography-600 font-urbanistSemiBold">
                    {searchError}
                  </Text>
                </View>
              ) : (
                searchSuggestions.map((suggestion) => (
                  <Pressable
                    key={suggestion.id}
                    onPress={() => handleSuggestionPress(suggestion)}
                    className="p-4 border-b border-gray-100"
                  >
                    <Text className="text-typography-800 font-urbanistSemiBold">
                      {suggestion.displayName}
                    </Text>
                    {suggestion.description && (
                      <Text className="text-typography-500 text-xs font-urbanist">
                        {suggestion.description}
                      </Text>
                    )}
                  </Pressable>
                ))
              )}
            </ScrollView>
          </View>
        )}
      </View>
    </TouchableWithoutFeedback>
  );
}
