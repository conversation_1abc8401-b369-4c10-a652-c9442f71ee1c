import { View, Text, Pressable } from 'react-native';
import { useRouter } from 'expo-router';
import { useEffect, useRef, useState } from 'react';
import SCREENS from '@/constants/Screens';
import Animated, {
  FadeOut,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  Layout,
  LinearTransition,
} from 'react-native-reanimated';

interface EventsSectionProps<T = any> {
  title: string;
  events: T[];
  eventType?: string | null;
  renderCard: (event: T) => React.ReactNode;
  visibleCount?: number;
  extraParams?: Record<string, any>;
  animated?: boolean; // NEW PROP
}

export default function EventsSection<T>({
  title,
  events,
  eventType = null,
  renderCard,
  visibleCount = 2,
  extraParams = {},
  animated = false, // DEFAULT TO FALSE
}: EventsSectionProps<T>) {
  const router = useRouter();
  const previousEventIds = useRef<Set<string>>(new Set());
  const isFirstRender = useRef(true);
  const [newItemIds, setNewItemIds] = useState<Set<string>>(new Set());

  const visibleEvents = events.slice(0, visibleCount);

  useEffect(() => {
    if (!animated) return;

    const currentEventIds = new Set(
      visibleEvents.map(
        (event, index) => (event as any)?.id?.toString() || `event-${index}`
      )
    );

    if (isFirstRender.current) {
      previousEventIds.current = currentEventIds;
      isFirstRender.current = false;
      return;
    }

    const newIds = new Set(
      [...currentEventIds].filter((id) => !previousEventIds.current.has(id))
    );

    setNewItemIds(newIds);
    previousEventIds.current = currentEventIds;

    if (newIds.size > 0) {
      setTimeout(() => setNewItemIds(new Set()), 1000);
    }
  }, [animated, visibleEvents.map((event) => (event as any)?.id).join(',')]);

  const handleSeeAll = () => {
    if (eventType) {
      router.push({
        pathname: SCREENS.VIEW_ALL,
        params: {
          eventType,
          ...extraParams,
        },
      });
    }
  };

  const getEventKey = (event: T, index: number) =>
    (event as any)?.id?.toString() || `event-${index}`;

  const AnimatedCard = ({ event, index }: { event: T; index: number }) => {
    const eventId = getEventKey(event, index);
    const isNew = newItemIds.has(eventId);

    const height = useSharedValue(isNew ? 0 : 1);
    const opacity = useSharedValue(isNew ? 0 : 1);
    const [shouldRender, setShouldRender] = useState(!isNew);

    useEffect(() => {
      if (animated && isNew) {
        setTimeout(() => setShouldRender(true), 30);
        height.value = withSpring(1, { damping: 15, stiffness: 150 });
        opacity.value = withSpring(1, { damping: 15, stiffness: 150 });
      }
    }, [animated, isNew]);

    const animatedStyle = useAnimatedStyle(() => ({
      transform: [{ scaleY: height.value }],
      opacity: opacity.value,
    }));

    if (!animated) {
      return <View key={eventId}>{renderCard(event)}</View>;
    }

    return (
      <Animated.View
        key={eventId}
        layout={LinearTransition.springify().damping(15).stiffness(150)}
        exiting={FadeOut.duration(200)}
      >
        {shouldRender && (
          <Animated.View style={animatedStyle}>
            {renderCard(event)}
          </Animated.View>
        )}
      </Animated.View>
    );
  };

  return (
    <View className="mt-6">
      <View className="flex flex-row justify-between items-center mb-4">
        <Text className="text-xl font-urbanistBold text-typography-900">
          {title}
        </Text>
        {events.length > visibleCount && (
          <Pressable onPress={handleSeeAll}>
            <Text className="text-primary-0 text-right font-urbanistSemiBold">
              See all →
            </Text>
          </Pressable>
        )}
      </View>
      {visibleEvents.map((event, index) => (
        <AnimatedCard
          key={getEventKey(event, index)}
          event={event}
          index={index}
        />
      ))}
    </View>
  );
}
