import React, { useState } from 'react';
import { Modal as RNModal, Pressable, View, Animated } from 'react-native';
import { Entypo } from '@expo/vector-icons';
import { Text } from '@/components/ui/text';
import { Icon } from '../ui/icon';
import { ChevronRightIcon } from 'lucide-react-native';

export default function FloatingActionMenu({
  actions,
}: {
  actions: {
    label: string;
    onPress: () => void;
    leftIcon?: React.ReactNode;
  }[];
}) {
  const [open, setOpen] = useState(false);
  const rotation = useState(new Animated.Value(0))[0];

  const toggleModal = () => {
    Animated.timing(rotation, {
      toValue: open ? 0 : 1,
      duration: 200,
      useNativeDriver: true,
    }).start();
    setOpen(!open);
  };

  const rotate = rotation.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '45deg'],
  });

  return (
    <>
      <Pressable
        onPress={toggleModal}
        className="absolute bottom-24 left-1/2 -translate-x-1/2 z-50 w-16 h-16 bg-primary-600 rounded-full justify-center items-center shadow-xl"
      >
        <Animated.View style={{ transform: [{ rotate }] }}>
          <Entypo name="plus" size={30} color="white" />
        </Animated.View>
      </Pressable>

      <RNModal
        visible={open}
        transparent
        animationType="fade"
        onRequestClose={toggleModal}
      >
        {/* Fullscreen clickable backdrop */}
        <Pressable
          onPress={toggleModal}
          className="flex-1 bg-black/30 items-center justify-end"
        >
          <View className="absolute bottom-44 bg-white shadow-xl rounded-xl w-[80%] max-w-sm px-8 py-3">
            {actions.map((action, idx) => (
              <View key={idx} className="w-full">
                <Pressable
                  onPress={() => {
                    toggleModal();
                    action.onPress();
                  }}
                  className="flex-row items-center justify-between py-4"
                >
                  {/* Left icon + label */}
                  <View className="flex-row items-center flex-1 min-w-0">
                    {action.leftIcon}
                    <Text
                      numberOfLines={1}
                      className="ml-3 text-base font-urbanistSemiBold text-typography-800 truncate"
                    >
                      {action.label}
                    </Text>
                  </View>

                  {/* Right chevron */}
                  <Icon as={ChevronRightIcon} className="text-typography-800" />
                </Pressable>

                {idx !== actions.length - 1 && (
                  <View className="border-t border-dashed border-gray-300" />
                )}
              </View>
            ))}
          </View>
        </Pressable>
      </RNModal>
    </>
  );
}
