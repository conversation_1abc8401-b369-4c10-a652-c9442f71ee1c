import { useEffect, useState } from 'react';
import { Icon } from '@/components/ui/icon';
import { SearchIcon } from 'lucide-react-native';
import { useDebounce } from '@/hooks/useDebounce';
import { CustomInput } from '@/components/ui/customInput';

interface SearchBarProps {
  value: string;
  onDebouncedChange: (text: string) => void;
  placeholder?: string;
  delay?: number;
}

export default function SearchBar({
  value,
  onDebouncedChange,
  placeholder = 'Search...',
  delay = 500,
}: SearchBarProps) {
  const [localValue, setLocalValue] = useState(value);
  const debounced = useDebounce(localValue, delay);

  useEffect(() => {
    setLocalValue(value);
  }, [value]);

  useEffect(() => {
    onDebouncedChange(debounced);
  }, [debounced]);

  return (
    <CustomInput
      value={localValue}
      onChangeText={setLocalValue}
      placeholder={placeholder}
      placeholderClassName="font-urbanist"
      inputFieldClassName="!pl-0"
      inputClassName="rounded-full bg-gray-100"
      className="mb-4"
      iconPosition="left"
      icon={
        <Icon as={SearchIcon} size="lg" className="text-typography-500 ml-3" />
      }
      type="text"
      returnKeyType="search"
    />
  );
}
