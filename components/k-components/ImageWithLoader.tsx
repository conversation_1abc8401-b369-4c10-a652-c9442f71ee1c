import { useState } from 'react';
import { ActivityIndicator, View, Image } from 'react-native';
import clsx from 'clsx';

interface ImageWithLoaderProps {
  uri: string;
  className?: string;
}

export function ImageWithLoader({ uri, className = '' }: ImageWithLoaderProps) {
  const [loading, setLoading] = useState(true);

  return (
    <View
      className={clsx(
        'overflow-hidden justify-center items-center bg-gray-100',
        className
      )}
    >
      {loading && (
        <ActivityIndicator
          size="small"
          color="#1DB960" // Kali green
          className="absolute"
        />
      )}
      <Image
        source={{ uri }}
        className="w-full h-full"
        resizeMode="cover"
        onLoadStart={() => setLoading(true)}
        onLoadEnd={() => setLoading(false)}
      />
    </View>
  );
}
