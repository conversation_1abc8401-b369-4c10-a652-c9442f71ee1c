import React, { useState } from 'react';
import { Pressable, View } from 'react-native';
import {
  FormControlLabel,
  FormControlLabelText,
} from '@/components/ui/form-control';
import {
  Actionsheet,
  ActionsheetBackdrop,
  ActionsheetContent,
} from '@/components/ui/actionsheet';
import { InfoIcon } from 'lucide-react-native';
import { Icon } from '../ui/icon';
import { Text } from '@/components/ui/text';
import { HStack } from '@/components/ui/hstack';

interface Props {
  label: string;
  info?: string;
}

const FormFieldLabel = ({ label, info }: Props) => {
  const [open, setOpen] = useState(false);

  return (
    <>
      <View className="flex-row gap-1 items-center">
        <FormControlLabel>
          <HStack className="items-center justify-between">
            <HStack className="items-center space-x-1">
              <FormControlLabelText className="font-urbanistSemiBold text-typography-700">
                {label}
              </FormControlLabelText>
            </HStack>
          </HStack>
        </FormControlLabel>
        {info && (
          <Pressable onPress={() => setOpen(true)} className="pl-2 pr-4 py-2">
            <Icon as={InfoIcon} size="sm" className="text-typography-400" />
          </Pressable>
        )}
      </View>

      {info && (
        <Actionsheet isOpen={open} onClose={() => setOpen(false)}>
          <ActionsheetBackdrop />
          <ActionsheetContent className="px-4 pt-6 pb-10">
            <HStack className=" flex flex-col items-center space-x-3 mt-4 justify-center">
              {/* @ts-ignore */}
              <Icon as={InfoIcon} size="2xl" className="text-blue-500 mb-2" />
              <Text className="text-base font-urbanistBold text-typography-800 mb-1">
                {label}
              </Text>
              <Text className="text-sm text-center font-urbanist text-typography-600 leading-relaxed">
                {info}
              </Text>
            </HStack>
          </ActionsheetContent>
        </Actionsheet>
      )}
    </>
  );
};

export default FormFieldLabel;
