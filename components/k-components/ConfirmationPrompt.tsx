import React from 'react';
import { View } from 'react-native';
import { Text } from '@/components/ui/text';
import { Button, ButtonText, ButtonSpinner } from '@/components/ui/button';

interface ConfirmationPromptProps {
  heading: string;
  description?: string;
  primaryText?: string;
  secondaryText?: string;
  onPrimaryPress: () => void;
  onSecondaryPress?: () => void;
  loading?: boolean;
  type?: 'warning' | 'error';
}

const ConfirmationPrompt = ({
  heading,
  description,
  primaryText = 'Confirm',
  secondaryText = 'Cancel',
  onPrimaryPress,
  onSecondaryPress,
  loading = false,
  type = 'warning',
}: ConfirmationPromptProps) => {
  return (
    <View className="rounded-xl w-full flex flex-col gap-2">
      <Text className="text-xl font-urbanistBold mb-3 text-center">
        {heading}
      </Text>

      {description && (
        <Text className="text-md text-typography-600 font-urbanistMedium text-center mb-4">
          {description}
        </Text>
      )}

      <View className="flex-row justify-between gap-3 w-full">
        {onSecondaryPress && (
          <Button
            variant="outline"
            className="flex-1 border border-gray-600"
            onPress={onSecondaryPress}
            disabled={loading}
          >
            <ButtonText className="text-gray-600  font-urbanistSemiBold">
              {secondaryText}
            </ButtonText>
          </Button>
        )}

        <Button
          className={`flex-1 ${
            type === 'error' ? 'bg-error-500' : 'bg-primary-0'
          }`}
          onPress={() => !loading && onPrimaryPress()}
        >
          {loading ? (
            <ButtonSpinner size="small" className="text-white" />
          ) : (
            <ButtonText className="font-urbanistSemiBold">
              {primaryText}
            </ButtonText>
          )}
        </Button>
      </View>
    </View>
  );
};

export default ConfirmationPrompt;
