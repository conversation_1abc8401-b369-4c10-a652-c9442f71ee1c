import { Text } from '@/components/ui/text';
import {
  AlertDialog,
  AlertDialogBackdrop,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogBody,
} from '@/components/ui/alert-dialog';
import { Heading } from '@/components/ui/heading';
import { Icon } from '@/components/ui/icon';
import { Button, ButtonText } from '../ui/button';
import { AlertTriangle } from 'lucide-react-native';

const WarningDialog = ({
  open,
  onClose,
  onConfirm,
  message,
}: {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  message: string;
}) => (
  <AlertDialog isOpen={open} onClose={onClose}>
    <AlertDialogBackdrop />
    <AlertDialogContent className="p-4 py-5 gap-4 max-w-[649px]">
      <AlertDialogBody contentContainerClassName="flex-col gap-4 items-center">
        {/* @ts-ignore */}
        <Icon as={AlertTriangle} size="2xl" className="text-yellow-600" />
        <Text className="text-typography-950 text-xl font-urbanistBold">
          Are you sure?
        </Text>
        <Text className="font-urbanistMedium text-center" size="sm">
          {message}
        </Text>
      </AlertDialogBody>
      <AlertDialogFooter className="w-full flex-row justify-center space-x-3">
        <Button
          variant="outline"
          action="secondary"
          onPress={onClose}
          size="md"
          className="w-1/2"
        >
          <ButtonText className="font-urbanistSemiBold">Cancel</ButtonText>
        </Button>
        <Button size="md" className="w-1/2 bg-primary-0" onPress={onConfirm}>
          <ButtonText className="font-urbanistSemiBold">Proceed</ButtonText>
        </Button>
      </AlertDialogFooter>
    </AlertDialogContent>
  </AlertDialog>
);

export default WarningDialog;
