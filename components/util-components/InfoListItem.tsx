import { View, Text } from 'react-native';
import { Icon } from '../ui/icon';

const InfoListItem = ({
  icon,
  value,
  numberOfLines = 1,
}: {
  icon: React.ElementType;
  value?: string | boolean;
  numberOfLines?: number;
}) =>
  value ? (
    <View className="mt-1.5">
      <View className="flex-row items-start gap-2">
        <Icon as={icon} size="sm" className="text-primary-0 mt-0.5" />
        <View className="flex-1">
          <Text
            className="text-sm text-typography-600 font-urbanistSemiBold"
            numberOfLines={numberOfLines}
          >
            {value}
          </Text>
        </View>
      </View>
    </View>
  ) : null;

export default InfoListItem;
