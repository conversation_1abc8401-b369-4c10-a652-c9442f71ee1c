import React from 'react';
import { View } from 'react-native';
import { Text } from '@/components/ui/text';
import InfoListItem from '@/components/util-components/InfoListItem';
import type { LucideIcon } from 'lucide-react-native';
import clsx from 'clsx';

interface InfoItem {
  icon: LucideIcon;
  value?: string;
  numberOfLines?: number;
}

interface Props {
  title: string;
  items: InfoItem[];
  headerRight?: React.ReactNode;
  className?: string;
}

const ListInfoCard = ({ title, items, headerRight, className }: Props) => {
  return (
    <View
      className={clsx(
        'flex-1 bg-white border border-gray-200 rounded-lg p-4 shadow-sm',
        className
      )}
    >
      <View className="flex-row items-center justify-between mb-2">
        <Text
          className="font-urbanistExtraBold text-typography-700 text-xl"
          numberOfLines={1}
          ellipsizeMode="tail"
        >
          {title}
        </Text>
        {headerRight ? <View>{headerRight}</View> : null}
      </View>

      <View className="border-t border-dashed border-gray-300 mb-1.5" />

      <View className="flex-col gap-1">
        {items.map((item, idx) => (
          <InfoListItem
            key={idx}
            icon={item.icon}
            value={item.value}
            numberOfLines={item.numberOfLines ?? 1}
          />
        ))}
      </View>
    </View>
  );
};

export default ListInfoCard;
