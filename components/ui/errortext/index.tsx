import React, { useEffect, useRef, useState } from 'react';
import {
  Animated,
  StyleSheet,
  Text as RNText,
  View,
  ViewStyle,
  TextStyle,
  LayoutChangeEvent,
} from 'react-native';
import { cn } from '@/utils';

interface ErrorTextProps {
  message?: string | null;
  className?: string;
  containerStyle?: ViewStyle;
  textStyle?: TextStyle;
}

export const ErrorText: React.FC<ErrorTextProps> = ({
  message,
  className = '',
  containerStyle,
  textStyle,
}) => {
  const [measuredHeight, setMeasuredHeight] = useState(0);
  const animatedHeight = useRef(new Animated.Value(0)).current;
  const animatedOpacity = useRef(new Animated.Value(0)).current;

  const onTextLayout = (event: LayoutChangeEvent) => {
    const height = event.nativeEvent.layout.height;
    if (height > 0) setMeasuredHeight(height);
  };

  useEffect(() => {
    Animated.parallel([
      Animated.timing(animatedHeight, {
        toValue: message ? measuredHeight : 0,
        duration: 200,
        useNativeDriver: false,
      }),
      Animated.timing(animatedOpacity, {
        toValue: message ? 1 : 0,
        duration: 200,
        useNativeDriver: false,
      }),
    ]).start();
  }, [message, measuredHeight]);

  return (
    <>
      {/* Invisible measurer, must be visible for proper height */}
      {message ? (
        <View style={styles.hiddenMeasure} onLayout={onTextLayout}>
          <RNText
            numberOfLines={0}
            className={cn(
              'text-error-500 text-sm font-urbanist text-center',
              className
            )}
            style={textStyle}
          >
            {message}
          </RNText>
        </View>
      ) : null}

      {/* Animated visible version */}
      <Animated.View
        className="self-center w-full"
        style={[
          styles.container,
          containerStyle,
          {
            height: animatedHeight,
            opacity: animatedOpacity,
            overflow: 'hidden',
          },
        ]}
      >
        <RNText
          numberOfLines={0}
          className={cn(
            'text-error-500 text-sm font-urbanist text-center',
            className
          )}
          style={textStyle}
        >
          {message ?? ''}
        </RNText>
      </Animated.View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  hiddenMeasure: {
    position: 'absolute',
    opacity: 0,
    zIndex: -1,
    left: 0,
    right: 0,
  },
});
