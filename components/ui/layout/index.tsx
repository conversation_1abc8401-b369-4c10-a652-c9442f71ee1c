import React from 'react';
import {
  SafeAreaView,
  ScrollView,
  View,
  StyleSheet,
  ViewStyle,
} from 'react-native';

interface LayoutProps {
  children: React.ReactNode;
  noScroll?: boolean;
  isFullscreen?: boolean;
  style?: ViewStyle;
}

export function Layout({
  children,
  noScroll = false,
  isFullscreen = true,
  style,
}: LayoutProps) {
  const containerStyle = [
    styles.container,
    { paddingBottom: isFullscreen ? 0 : 80 },
    style,
  ];

  return (
    <SafeAreaView style={styles.safe} className="bg-red-400">
      {noScroll ? (
        <View style={containerStyle}>{children}</View>
      ) : (
        <ScrollView
          keyboardShouldPersistTaps="handled"
          contentContainerStyle={containerStyle}
          showsVerticalScrollIndicator={false}
        >
          {children}
        </ScrollView>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safe: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    paddingHorizontal: 16,
    paddingTop: 16,
    flexGrow: 1,
  },
});
