import {
  But<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ButtonIcon,
} from '@/components/ui/button';
import { LucideIcon } from 'lucide-react-native';
import { View } from 'react-native';

interface CTAButtonProps {
  onPress: () => void | Promise<void>;
  loading?: boolean;
  lefticon?: LucideIcon;
  isFormValid?: boolean;
  title: string;
}

export function CTAButton({
  onPress,
  loading = false,
  isFormValid = true,
  title,
  lefticon,
}: CTAButtonProps) {
  return (
    <Button
      onTouchStart={onPress}
      isDisabled={!isFormValid}
      className={`mt-3 font-urbanistBold ${
        isFormValid ? 'bg-primary-0' : 'bg-typography-300'
      }`}
    >
      {loading ? (
        <ButtonSpinner size="small" className="text-white" />
      ) : (
        <>
          {lefticon && <ButtonIcon as={lefticon} />}
          <ButtonText
            className={`font-urbanistSemiBold ${
              isFormValid ? 'text-white' : 'text-typography-500'
            }`}
          >
            {title}
          </ButtonText>
        </>
      )}
    </Button>
  );
}
