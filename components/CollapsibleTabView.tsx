import React, { useMemo } from 'react';
import { Dimensions } from 'react-native';
import { Tabs, MaterialTabBar } from 'react-native-collapsible-tab-view';
import { TabColors } from '@/constants/Colors';
import { CollapsibleTabViewProps, TabBarConfig } from '@/types/tab';

const defaultTabBarConfig: TabBarConfig = {
  scrollEnabled: false,
  fontFamily: 'Urbanist_600SemiBold',
  fontWeight: '600',
  textTransform: 'capitalize',
  textAlign: 'center',
  paddingVertical: 8,
  paddingHorizontal: 0,
  margin: 0,
};

export default function CollapsibleTabView<T = any>({
  data,
  tabs,
  tabBarConfig = {},
  lazy = false,
  renderHeader,
  headerHeight,
  initialTabName,
  onTabChange,
  showsVerticalScrollIndicator = false,
  tabFilter,
}: CollapsibleTabViewProps<T>) {
  const mergedTabBarConfig = { ...defaultTabBarConfig, ...tabBarConfig };

  const visibleTabs = useMemo(() => {
    if (tabFilter) {
      return tabs.filter((tab) => tabFilter(tab, data));
    }
    return tabs;
  }, [tabs, data, tabFilter]);

  const screenWidth = Dimensions.get('window').width;
  const calculatedTabWidth =
    mergedTabBarConfig.minWidth || screenWidth / visibleTabs.length;

  const containerProps = {
    lazy,
    ...(initialTabName && { initialTabName }),
    ...(onTabChange && { onTabChange }),
    ...(renderHeader && { renderHeader }),
    ...(headerHeight && { headerHeight }),
  };

  return (
    <Tabs.Container
      {...containerProps}
      renderTabBar={(props) => (
        <MaterialTabBar
          {...props}
          scrollEnabled={mergedTabBarConfig.scrollEnabled}
          indicatorStyle={{
            backgroundColor: TabColors.active,
            height: 3,
          }}
          activeColor={TabColors.active}
          inactiveColor={TabColors.inactive}
          labelStyle={{
            textAlign: mergedTabBarConfig.textAlign,
            fontFamily: mergedTabBarConfig.fontFamily,
            fontWeight: mergedTabBarConfig.fontWeight,
            minWidth: calculatedTabWidth,
            margin: mergedTabBarConfig.margin,
            paddingHorizontal: mergedTabBarConfig.paddingHorizontal,
            paddingVertical: mergedTabBarConfig.paddingVertical,
            textTransform: mergedTabBarConfig.textTransform,
          }}
          tabStyle={mergedTabBarConfig.tabStyle}
          contentContainerStyle={mergedTabBarConfig.contentContainerStyle}
          style={{
            borderBottomWidth: 1,
            borderBottomColor: TabColors.border,
          }}
        />
      )}
    >
      {visibleTabs.map((tab) => (
        <Tabs.Tab name={tab.id} key={tab.id}>
          <Tabs.ScrollView
            showsVerticalScrollIndicator={showsVerticalScrollIndicator}
          >
            {tab.render(data)}
          </Tabs.ScrollView>
        </Tabs.Tab>
      ))}
    </Tabs.Container>
  );
}
