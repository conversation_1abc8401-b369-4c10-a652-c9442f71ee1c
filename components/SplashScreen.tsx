import React, { useEffect, useRef } from 'react';
import { Animated, StyleSheet, View, Image, Dimensions } from 'react-native';

const { width } = Dimensions.get('window');

interface AnimatedSplashProps {
  onAnimationEnd: () => void;
}

export default function AnimatedSplash({
  onAnimationEnd,
}: AnimatedSplashProps) {
  const bounceAnim = useRef(new Animated.Value(-200)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const opacityAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    // Bounce + Zoom
    Animated.sequence([
      Animated.spring(bounceAnim, {
        toValue: 0,
        useNativeDriver: true,
        friction: 4,
        tension: 100,
      }),
      Animated.parallel([
        Animated.timing(scaleAnim, {
          toValue: 300,
          duration: 500, // ⬅️ Faster zoom (was 1800, now 800ms)
          useNativeDriver: true,
        }),
      ]),
    ]).start(() => {
      onAnimationEnd();
    });
  }, []);

  return (
    <View style={styles.container}>
      <Animated.View
        style={{
          transform: [{ translateY: bounceAnim }, { scale: scaleAnim }],
          opacity: opacityAnim,
        }}
      >
        <Image
          source={require('@/assets/Icons/pngs/kali-logo.png')}
          style={styles.logo}
          resizeMode="contain"
        />
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    justifyContent: 'center',
    alignItems: 'center',
  },
  logo: {
    width: width * 0.4,
    height: width * 0.4,
    tintColor: '#1db960',
  },
});
