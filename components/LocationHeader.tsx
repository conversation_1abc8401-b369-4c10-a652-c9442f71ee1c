import { useState, useCallback } from 'react';
import { Pressable, Text, View } from 'react-native';
import { useRouter, useFocusEffect } from 'expo-router';
import { useLocationStorage } from '@/hooks/useLocationStorage';
import { SkeletonText } from '@/components/ui/skeleton';
import { Icon } from '@/components/ui/icon';
import { ChevronDown, MapPinIcon, ListFilterIcon } from 'lucide-react-native';
import { LocationData } from '@/types/location-types';
import { fetchLocation } from '@/utils/location-utils';
import SearchBar from './k-components/SearchBar';
import SCREENS from '@/constants/Screens';

const LoadingSkeletion = () => {
  return (
    <View className="gap-2 flex flex-col">
      <SkeletonText _lines={1} className="w-[70px] h-4" />
      <SkeletonText _lines={1} className="w-[140px] h-3" />
    </View>
  );
};

export function LocationHeader() {
  const router = useRouter();
  const { getLocation, saveLocation } = useLocationStorage();
  const [location, setLocation] = useState<LocationData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  const loadLocation = useCallback(async () => {
    try {
      const loc = await getLocation();
      if (loc) {
        setLocation(loc);
      } else {
        const liveLoc = await fetchLocation();
        saveLocation(liveLoc);
        setLocation(liveLoc);
      }
    } catch (error) {
      setLocation({
        id: `generated-fallback-${Date.now()}`,
        displayName: 'World',
        description: '',
        data: null,
      });
    } finally {
      setLoading(false);
    }
  }, [getLocation]);

  useFocusEffect(
    useCallback(() => {
      loadLocation();
    }, [loadLocation])
  );

  return (
    <View className="flex-1 gap-5">
      <View className="w-full flex flex-row justify-between">
        <Pressable
          onPress={() => router.push(SCREENS.LOCATION_SEARCH)}
          className="self-start max-w-[70%]"
        >
          {loading ? (
            <LoadingSkeletion />
          ) : (
            <View>
              <View className="flex flex-row items-center gap-1">
                <Icon as={MapPinIcon} size="lg" className="text-primary-0" />
                <Text
                  numberOfLines={1}
                  ellipsizeMode="tail"
                  className="font-urbanistExtraBold text-xl text-primary-0 max-w-[70%]"
                >
                  {location?.displayName}
                </Text>
                <Icon
                  as={ChevronDown}
                  size="sm"
                  className="text-typography-600 font-bold self-center mt-1"
                />
              </View>
              <Text
                numberOfLines={1}
                ellipsizeMode="tail"
                className="font-urbanistSemiBold text-sm text-typography-600 pl-1"
              >
                {location?.description}
              </Text>
            </View>
          )}
        </Pressable>
        <Icon
          as={ListFilterIcon}
          size="xl"
          className="text-typography-600 self-center"
        />
      </View>
      <SearchBar
        value={''}
        onDebouncedChange={() => {}}
        placeholder={'Search tournaments and matches...'}
      />
    </View>
  );
}
