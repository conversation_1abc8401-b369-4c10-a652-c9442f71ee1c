import { View, Text } from 'react-native';
import DateTimePicker, {
  DateType,
  useDefaultStyles,
} from 'react-native-ui-datepicker';
import dayjs from 'dayjs';
import advancedFormat from 'dayjs/plugin/advancedFormat';
dayjs.extend(advancedFormat);

interface DateRangePickerFieldProps {
  value: {
    startDate?: string;
    endDate?: string;
  };
  onChange: (v: { startDate?: string; endDate?: string }) => void;
  showSelectedDates?: boolean;
}

export default function DateRangePickerField({
  value,
  onChange,
  showSelectedDates = true,
}: DateRangePickerFieldProps) {
  const defaultStyles = useDefaultStyles();

  const primaryColor = '#1DB960';

  const customStyles = {
    ...defaultStyles,
    selected: {
      backgroundColor: primaryColor,
    },
    selectedRange: {
      backgroundColor: primaryColor,
      opacity: 0.2,
    },
    today: {
      backgroundColor: 'transparent',
    },
    today_label: {
      color: primaryColor,
    },
  };

  const formatDate = (date: string | undefined) => {
    return date ? dayjs(date).format('Do MMM YYYY') : '';
  };

  const handleDateChange = ({
    startDate,
    endDate,
  }: {
    startDate: DateType;
    endDate: DateType;
  }) => {
    onChange({
      startDate: startDate ? dayjs(startDate).format('YYYY-MM-DD') : undefined,
      endDate: endDate ? dayjs(endDate).format('YYYY-MM-DD') : undefined,
    });
  };

  const parsedStartDate = value?.startDate
    ? dayjs(value.startDate).toDate()
    : undefined;

  const parsedEndDate = value?.endDate
    ? dayjs(value.endDate).toDate()
    : undefined;

  return (
    <View className="flex flex-col">
      <DateTimePicker
        classNames={{
          today_label: 'font-urbanistSemiBold',
          selected_label: 'font-urbanistBold',
          month_label: 'font-urbanistBold',
          year_label: 'font-urbanistBold',
          month_selector_label: 'font-urbanistSemiBold',
          year_selector_label: 'font-urbanistSemiBold',
          day_label: 'font-urbanist',
          weekday_label: 'font-urbanistSemiBold',
        }}
        mode="range"
        startDate={parsedStartDate}
        endDate={parsedEndDate}
        onChange={handleDateChange}
        styles={customStyles}
        minDate={new Date()}
        showOutsideDays={true}
      />

      {showSelectedDates && (
        <View className="flex-row justify-center gap-2 mt-2">
          <Text className="font-urbanistSemiBold text-typography-800">
            {parsedStartDate
              ? formatDate(value.startDate)
              : 'Pick a start date'}
          </Text>
          <Text className="text-typography-600 font-urbanist">
            {parsedStartDate || parsedEndDate ? 'to' : 'and'}
          </Text>
          <Text className="font-urbanistSemiBold text-typography-800">
            {parsedEndDate ? formatDate(value.endDate) : 'Pick an end date'}
          </Text>
        </View>
      )}
    </View>
  );
}
