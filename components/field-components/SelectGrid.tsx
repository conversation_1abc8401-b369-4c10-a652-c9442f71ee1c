import { useRef } from 'react';
import { View, Text, Pressable } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
} from 'react-native-reanimated';

interface Option {
  displayName: string;
  value: string;
  iconComponent?: any;
  iconName?: string;
  iconColor?: string;
}

interface SelectGridProps {
  options: Option[];
  defaultValue?: string;
  onChange: (value: string) => void;
}

const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

export function SelectGrid({
  options,
  defaultValue = '',
  onChange,
}: SelectGridProps) {
  const selected = defaultValue;

  const scales = useRef(options.map(() => useSharedValue(1))).current;

  const handleSelect = (value: string) => {
    onChange(value);
  };

  return (
    <View className="flex flex-row flex-wrap gap-3 justify-between">
      {options.map((option, index) => {
        const isSelected = selected && selected === option.value;
        const IconComponent = option.iconComponent;

        const animatedStyle = useAnimatedStyle(() => ({
          transform: [{ scale: scales[index].value }],
        }));

        return (
          <AnimatedPressable
            key={`${option.value}${index}`}
            onPress={() => handleSelect(option.value)}
            onPressIn={() => {
              scales[index].value = withSpring(0.95, { damping: 10 });
            }}
            onPressOut={() => {
              scales[index].value = withSpring(1, { damping: 10 });
            }}
            style={animatedStyle}
            className={`w-[48%] h-32 rounded-2xl border transition-colors duration-300 ${
              isSelected ? 'border-2 border-typography-900' : 'border-gray-300'
            }`}
          >
            <View className="flex-1 justify-center items-center">
              {IconComponent && option.iconName && (
                <IconComponent
                  name={option.iconName}
                  size={32}
                  color={option.iconColor || '#404040'}
                />
              )}
              <Text
                className={`mt-2 text-center text-base font-urbanistSemiBold text-typography-900`}
              >
                {option.displayName}
              </Text>
            </View>
          </AnimatedPressable>
        );
      })}
    </View>
  );
}
