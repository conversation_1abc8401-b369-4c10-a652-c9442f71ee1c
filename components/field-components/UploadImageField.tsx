import { useState } from 'react';
import { View, Text, Image, ActivityIndicator } from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import { Button } from '../ui/button';
import { Icon } from '../ui/icon';
import { CircleCheck } from 'lucide-react-native';
import { ImageWithLoader } from '../k-components/ImageWithLoader';
import { useImageUpload } from '@/hooks/useImageUpload';

const MAX_UPLOAD_FILE_MB = 5;
const MAX_FILE_SIZE_KB = MAX_UPLOAD_FILE_MB * 1024;
const ALLOWED_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image'];

interface UploadImageFieldProps {
  value: string;
  onChange: (url: string) => void;
  bucketName: string;
  folderPath?: string;
}

export default function UploadImageField({
  value,
  onChange,
  bucketName,
  folderPath,
}: UploadImageFieldProps) {
  const [localUri, setLocalUri] = useState(value || '');
  const [uploaded, setUploaded] = useState(false);

  const { uploadImage, isUploading, error, clearError, setError } =
    useImageUpload({
      bucketName,
      folderPath,
    });

  const pickImage = async () => {
    clearError();
    setUploaded(false);
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 1,
      base64: false,
    });

    if (result.canceled || !result.assets || result.assets.length === 0) return;

    const image = result.assets[0];
    const fileSizeInKB = image.fileSize ? image.fileSize / 1024 : 0;
    const type = image.mimeType || image.type || '';

    if (fileSizeInKB > MAX_FILE_SIZE_KB) {
      setError(`Image must be smaller than ${MAX_UPLOAD_FILE_MB} MB.`);
      return;
    }

    if (!ALLOWED_TYPES.includes(type)) {
      setError('Only JPG, JPEG, or PNG files are allowed.');
      return;
    }

    setLocalUri(image.uri);

    const publicUrl = await uploadImage(image.uri);
    if (publicUrl) {
      onChange(publicUrl);
      setUploaded(true);
    } else {
      setLocalUri('');
      setUploaded(false);
    }
  };

  const clearUpload = () => {
    setLocalUri('');
    setUploaded(false);
    onChange('');
  };

  return (
    <View className="flex flex-col items-center gap-4 mt-2 border border-dashed border-gray-300 p-4 rounded-lg">
      {!localUri && (
        <>
          <Button variant="link" onPress={pickImage}>
            <Text className="text-center text-primary-0 font-urbanistSemiBold border-b border-dotted border-primary-0">
              Tap to upload photo
            </Text>
          </Button>
          <Text className="text-xs text-gray-500">
            JPG/PNG | Max {MAX_UPLOAD_FILE_MB} MB
          </Text>
        </>
      )}

      {localUri && !uploaded && (
        <>
          <Image
            source={{ uri: localUri }}
            className="w-28 h-28 rounded-md"
            resizeMode="cover"
          />

          {isUploading && (
            <>
              <ActivityIndicator size="small" color="#1DB960" />
              <Text className="text-sm text-typography-600 font-urbanistSemiBold">
                Uploading image...
              </Text>
            </>
          )}

          <Button variant="link" size="md" onPress={clearUpload}>
            <Text className="text-red-500 border-b border-red-500 mt-2 font-urbanistSemiBold text-md">
              Clear Upload
            </Text>
          </Button>
        </>
      )}

      {uploaded && value && (
        <>
          <View className="flex flex-row gap-1 items-center">
            <Icon as={CircleCheck} size="lg" className="text-green-600" />
            <Text className="text-green-600 font-urbanistBold">
              Upload Complete
            </Text>
          </View>

          <ImageWithLoader uri={value} className="w-28 h-28 rounded-md" />
          <Button
            variant="link"
            size="md"
            onPress={() => {
              setLocalUri('');
              setUploaded(false);
              onChange('');
            }}
          >
            <Text className="text-red-500 border-b border-red-500 mt-2 font-urbanistSemiBold text-md">
              Clear Upload
            </Text>
          </Button>
        </>
      )}

      {error && (
        <Text className="text-red-500 text-sm text-center font-urbanistSemiBold">
          {error}
        </Text>
      )}
    </View>
  );
}
