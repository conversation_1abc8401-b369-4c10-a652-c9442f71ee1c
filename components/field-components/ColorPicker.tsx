import React, { useState } from 'react';
import { View, Text, Pressable, Keyboard } from 'react-native';
import { VStack } from '../ui/vstack';
import { HStack } from '../ui/hstack';
import { Button } from '../ui/button';
import { ButtonText } from '../ui/button';
import {
  Actionsheet,
  ActionsheetBackdrop,
  ActionsheetContent,
  ActionsheetDragIndicator,
  ActionsheetDragIndicatorWrapper,
} from '../ui/actionsheet';

interface ColorPickerProps {
  value: string | undefined;
  onChange: (color: string) => void;
  label?: string;
}

const PRESET_COLORS = [
  '#FF0000', // Red
  '#0000FF', // Blue
  '#00FF00', // Neon Green
  '#FFFF00', // Yellow
  '#FFA500', // Orange
  '#FFFFFF', // White
  '#000000', // Black
  '#800080', // Purple
  '#FFC0CB', // Pink
  '#808080', // <PERSON>
  '#8B0000', // Dark Red (Maroon)
  '#1E90FF', // Dodger Blue
  '#006400', // <PERSON> Green
  '#FFD700', // Gold
  '#A52A2A', // <PERSON>
  '#708090', // Slate Gray
  '#2E8B57', // Sea Green
  '#FF4500', // Orange Red
  '#DC143C', // Crimson
  '#4682B4', // Steel Blue
  '#191970', // Midnight Blue
  '#DAA520', // Goldenrod
  '#00CED1', // Dark Turquoise
  '#5F9EA0', // Cadet Blue
  '#B22222', // Firebrick
  '#00BFFF', // Deep Sky Blue
  '#32CD32', // Lime Green
  '#4B0082', // Indigo
  '#FF1493', // Deep Pink
  '#ADFF2F', // Green Yellow
  '#6A5ACD', // Slate Blue
  '#2F4F4F', // Dark Slate Gray
  '#7FFF00', // Chartreuse
  '#FF69B4', // Hot Pink
  '#F0E68C', // Khaki
  '#D2691E', // Chocolate
  '#20B2AA', // Light Sea Green
  '#9932CC', // Dark Orchid
  '#CD5C5C', // Indian Red
  '#B0C4DE', // Light Steel Blue
];

export default function ColorPicker({
  value,
  onChange,
  label,
}: ColorPickerProps) {
  const [isActionsheetVisible, setIsActionsheetVisible] = useState(false);

  const handleColorSelect = (color: string) => {
    onChange(color);
    setIsActionsheetVisible(false);
    Keyboard.dismiss();
  };

  const displayColor = value || 'transparent';

  return (
    <View>
      <Pressable
        onPress={() => setIsActionsheetVisible(true)}
        className="flex-row items-center justify-between p-4 border border-gray-300 rounded-lg bg-white"
      >
        <View className="flex-row items-center gap-3">
          <View
            className="w-8 h-8 rounded-full border border-gray-300"
            style={{ backgroundColor: displayColor }}
          />
          <Text className="text-base font-urbanistMedium text-typography-800">
            {value ? value.toUpperCase() : 'Select Color'}
          </Text>
        </View>
        <Text className="text-typography-500 font-urbanistMedium">
          Tap to change
        </Text>
      </Pressable>

      <Actionsheet
        isOpen={isActionsheetVisible}
        onClose={() => setIsActionsheetVisible(false)}
      >
        <ActionsheetBackdrop />
        <ActionsheetContent className="">
          <ActionsheetDragIndicatorWrapper>
            <ActionsheetDragIndicator />
          </ActionsheetDragIndicatorWrapper>

          <VStack className="space-y-4 mt-3">
            <Text className="text-xl font-urbanistBold text-center text-typography-900 mb-4">
              {label || 'Select Color'}
            </Text>

            <View className="flex-row flex-wrap gap-3 justify-center">
              {PRESET_COLORS.map((color) => (
                <Pressable
                  key={color}
                  onPress={() => handleColorSelect(color)}
                  className={`w-12 h-12 rounded-full border-2 ${
                    value === color ? 'border-primary-500' : 'border-gray-300'
                  }`}
                  style={{ backgroundColor: color }}
                >
                  {value === color && (
                    <View className="flex-1 items-center justify-center">
                      <Text className="text-white font-urbanistBold text-lg">
                        ✓
                      </Text>
                    </View>
                  )}
                </Pressable>
              ))}
            </View>

            <HStack className="space-x-3 pt-4 gap-3">
              <Button
                variant="outline"
                className="flex-1"
                onPress={() => setIsActionsheetVisible(false)}
              >
                <ButtonText className="font-urbanistSemiBold">
                  Cancel
                </ButtonText>
              </Button>
              <Button
                className="flex-1 bg-primary-0"
                onPress={() => setIsActionsheetVisible(false)}
              >
                <ButtonText className="font-urbanistSemiBold">Done</ButtonText>
              </Button>
            </HStack>
          </VStack>
        </ActionsheetContent>
      </Actionsheet>
    </View>
  );
}
