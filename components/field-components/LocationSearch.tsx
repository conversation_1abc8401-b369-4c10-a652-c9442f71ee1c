import { useLocationSuggestions } from '@/hooks/useLocationSuggestions';
import SearchBox from '@/components/k-components/SearchBox';

interface LocationSearchProps {
  onChange: (value: any) => void;
}

export default function LocationSearch({ onChange }: LocationSearchProps) {
  const { searchSuggestions, fetchSuggestions, searchError } =
    useLocationSuggestions({
      types: ['state', 'region', 'city'],
    });

  return (
    <SearchBox
      placeholder="Search a city, state or region..."
      searchSuggestions={searchSuggestions}
      onSearchChange={fetchSuggestions}
      searchError={searchError}
      onSuggestionSelect={(data) => {
        onChange(data);
      }}
    />
  );
}
