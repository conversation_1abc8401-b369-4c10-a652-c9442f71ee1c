// hooks/useStatusBarColor.ts
import { useFocusEffect } from 'expo-router';
import { useCallback } from 'react';
import { StatusBar as RNStatusBar } from 'react-native';

export function useStatusBarColor(
  color: string = '#1db960',
  barStyle: 'light-content' | 'dark-content' = 'light-content'
) {
  useFocusEffect(
    useCallback(() => {
      RNStatusBar.setBackgroundColor(color);
      RNStatusBar.setBarStyle(barStyle);

      return () => {
        RNStatusBar.setBackgroundColor('#ffffff');
        RNStatusBar.setBarStyle('dark-content');
      };
    }, [color, barStyle])
  );
}
