import { useState, useCallback } from 'react';
import axios from 'axios';
import locationErrorCodes from '@/lib/locationSearchErrorCodes';
import { API_URLS } from '@/utils/constants';

const LOCATION_IQ_API_KEY = 'pk.87885862fb1a11db8a001ee50d1b1086';

interface SearchSuggestion {
  id: string;
  displayName: string;
  description?: string;
  data: any;
}

interface UseLocationSuggestionsOptions {
  types?: string[];
  limit?: number;
  countrycodes?: string;
}

export function useLocationSuggestions(
  options?: UseLocationSuggestionsOptions
) {
  const [searchSuggestions, setSearchSuggestions] = useState<
    SearchSuggestion[]
  >([]);
  const [searchError, setSearchError] = useState<string | null>(null);
  const [isCooldown, setIsCooldown] = useState(false);

  const fetchSuggestions = useCallback(
    async (searchText: string) => {
      if (!searchText.trim() || isCooldown) {
        setSearchSuggestions([]);
        setSearchError(null);
        return;
      }

      try {
        const typesToSearch = options?.types ?? ['state', 'region', 'city'];
        const limit = options?.limit ?? 5;
        const countrycodes = options?.countrycodes; // optional

        const url = `${
          API_URLS.LOCATION_IQ_URL
        }?key=${LOCATION_IQ_API_KEY}&q=${encodeURIComponent(
          searchText
        )}&limit=${limit}&normalizecity=1&accept-language=en${
          countrycodes ? `&countrycodes=${countrycodes}` : ''
        }`;

        const response = await axios.get(url);

        const places = response.data as any[];
        const seenDisplayNames = new Set<string>();
        const filteredPlaces = places.filter((place) => {
          const placeType = place.type || place.place_type;
          const displayName = place.display_place || place.display_name;
          const typeAllowed = typesToSearch.includes(placeType);

          if (!typeAllowed) return false;
          if (seenDisplayNames.has(displayName)) {
            return false;
          }

          seenDisplayNames.add(displayName);
          return true;
        });

        const suggestions: SearchSuggestion[] = filteredPlaces.map(
          (place, index) => ({
            id: place.place_id
              ? String(place.place_id)
              : `generated-${index}-${Math.random().toString(36).substr(2, 9)}`,
            displayName: place.display_place || place.display_name,
            description: place.display_address || '',
            data: place,
          })
        );

        setSearchSuggestions(suggestions);
        setSearchError(null);
      } catch (error: any) {
        const status = error.response?.status;
        const message =
          locationErrorCodes[status] ||
          'Something went wrong. Please try again!';
        setSearchSuggestions([]);
        setSearchError(message);

        if (status === 429) {
          setIsCooldown(true);
          setTimeout(() => setIsCooldown(false), 3000);
        }
      }
    },
    [isCooldown, options?.types, options?.limit, options?.countrycodes]
  );

  return {
    searchSuggestions,
    searchError,
    fetchSuggestions,
  };
}
