import { useEffect, useRef, useState } from 'react';
import { useRouter } from 'expo-router';
import { useNetworkState, getNetworkStateAsync } from 'expo-network';
import { AppState, AppStateStatus } from 'react-native';
import SCREENS from '@/constants/Screens';

export function useConnectionMonitor() {
  const network = useNetworkState();
  const router = useRouter();
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [isChecking, setIsChecking] = useState(false);
  const [appState, setAppState] = useState<AppStateStatus>(
    AppState.currentState
  );

  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      setAppState(nextAppState);
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange
    );

    return () => {
      subscription?.remove();
    };
  }, []);

  useEffect(() => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }
    if (appState !== 'active' || !network) {
      return;
    }

    if (
      network?.isConnected === false ||
      network?.isInternetReachable === false
    ) {
      debounceTimeoutRef.current = setTimeout(async () => {
        if (AppState.currentState !== 'active' || isChecking) {
          return;
        }

        setIsChecking(true);

        try {
          const currentState = await getNetworkStateAsync();
          if (
            AppState.currentState === 'active' &&
            (currentState?.isConnected === false ||
              currentState?.isInternetReachable === false)
          ) {
            router.push(SCREENS.NO_INTERNET);
          }
        } catch (error) {
          if (AppState.currentState === 'active') {
            router.push(SCREENS.NO_INTERNET);
          }
        } finally {
          setIsChecking(false);
        }
      }, 1500);
    }

    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, [
    network?.isConnected,
    network?.isInternetReachable,
    appState,
    isChecking,
  ]);

  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);
}
