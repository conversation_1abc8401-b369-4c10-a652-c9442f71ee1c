import { useEffect, useState } from 'react';
import { useRouter } from 'expo-router';
import * as Linking from 'expo-linking';
import { supabase } from '@/lib/supabase';
import SCREENS from '@/constants/Screens';
import { toast } from '@/toast/toast';

interface DeepLinkHandler {
  (url: string): Promise<boolean> | boolean;
}

interface UseDeepLinkingOptions {
  handlers?: DeepLinkHandler[];
}

interface UseDeepLinkingReturn {
  isReady: boolean;
  initialUrl: string | null;
}
export function useDeepLinking(
  options: UseDeepLinkingOptions = {}
): UseDeepLinkingReturn {
  const router = useRouter();
  const [isReady, setIsReady] = useState(false);
  const [initialUrl, setInitialUrl] = useState<string | null>(null);
  const { handlers = [] } = options;

  const handleDeepLink = async (url: string): Promise<boolean> => {
    try {
      const parsed = Linking.parse(url);

      const access_token = Array.isArray(parsed.queryParams?.access_token)
        ? parsed.queryParams?.access_token[0]
        : parsed.queryParams?.access_token;

      const type = Array.isArray(parsed.queryParams?.type)
        ? parsed.queryParams?.type[0]
        : parsed.queryParams?.type;

      if (access_token && type === 'recovery') {
        await supabase.auth.setSession({
          access_token,
          refresh_token: '',
        });
        router.replace(SCREENS.SET_NEW_PASSWORD);
        return true;
      }

      for (const handler of handlers) {
        const handled = await handler(url);
        if (handled) {
          return true;
        }
      }

      return false;
    } catch (error) {
      toast.error('Failed to load link');
      return false;
    }
  };

  useEffect(() => {
    let subscription: any = null;

    const setupDeepLinking = async () => {
      try {
        const initialURL = await Linking.getInitialURL();

        if (initialURL) {
          setInitialUrl(initialURL);
          await handleDeepLink(initialURL);
        }

        subscription = Linking.addEventListener('url', async (event) => {
          await handleDeepLink(event.url);
        });
      } catch (error) {
        toast.error('Failed to setup linking');
      } finally {
        setIsReady(true);
      }
    };

    setupDeepLinking();

    return () => {
      subscription?.remove();
    };
  }, [handlers]);

  return {
    isReady,
    initialUrl,
  };
}
