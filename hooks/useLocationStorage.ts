import AsyncStorage from '@react-native-async-storage/async-storage';
import { useCallback } from 'react';
import { LocationData } from '@/types/location-types';

const STORAGE_KEY = 'location_storage_key';

export function useLocationStorage() {
  const saveLocation = useCallback(
    async (location: LocationData): Promise<void> => {
      try {
        const jsonValue = JSON.stringify(location);
        await AsyncStorage.setItem(STORAGE_KEY, jsonValue);
      } catch (error) {
        console.log('[LocationStorage] Failed to save location:', error);
      }
    },
    []
  );

  const getLocation = useCallback(async (): Promise<LocationData | null> => {
    try {
      const stored = await AsyncStorage.getItem(STORAGE_KEY);
      return stored ? (JSON.parse(stored) as LocationData) : null;
    } catch (error) {
      console.log('[LocationStorage] Failed to load location:', error);
      return null;
    }
  }, []);

  const clearLocation = useCallback(async (): Promise<void> => {
    try {
      await AsyncStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.log('[LocationStorage] Failed to clear location:', error);
    }
  }, []);

  return {
    saveLocation,
    getLocation,
    clearLocation,
  };
}
