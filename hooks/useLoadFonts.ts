// hooks/useLoadFonts.ts
import { useEffect } from 'react';
import { useFonts } from '@expo-google-fonts/urbanist';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import * as NavigationBar from 'expo-navigation-bar';
import {
  Urbanist_400Regular,
  Urbanist_500Medium,
  Urbanist_600SemiBold,
  Urbanist_700Bold,
  Urbanist_800ExtraBold,
} from '@expo-google-fonts/urbanist';

export function useLoadFonts() {
  const [fontsLoaded, fontsError] = useFonts({
    Urbanist_400Regular,
    Urbanist_500Medium,
    Urbanist_600SemiBold,
    Urbanist_700Bold,
    Urbanist_800ExtraBold,
    ...FontAwesome.font,
  });

  useEffect(() => {
    if (fontsError) throw fontsError;

    if (fontsLoaded) {
      NavigationBar.setBackgroundColorAsync('#ffffff');
      NavigationBar.setButtonStyleAsync('dark');
    }
  }, [fontsLoaded, fontsError]);

  return { fontsLoaded, fontsError };
}
