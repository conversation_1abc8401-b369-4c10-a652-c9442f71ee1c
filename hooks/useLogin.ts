import { useState } from 'react';
import { supabase } from '@/lib/supabase';
import {
  handleAuthError,
  getAuthErrorMessage,
} from '@/utils/auth-error-handler';

export function useLogin() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  async function login(email: string, password: string): Promise<boolean> {
    setLoading(true);
    setError(null);

    try {
      const { error: loginError } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (loginError) {
        await handleAuthError(loginError, false);

        const errorMessage = loginError.code
          ? getAuthErrorMessage(loginError.code)
          : loginError.message || 'Something went wrong';
        setError(errorMessage);
        return false;
      }

      return true;
    } catch (err: any) {
      await handleAuthError(err, false);
      setError('An unexpected error occurred');
      return false;
    } finally {
      setLoading(false);
    }
  }

  return { login, loading, error };
}
