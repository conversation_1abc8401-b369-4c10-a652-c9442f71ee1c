import { useState } from 'react';
import { supabase } from '@/lib/supabase';
import { generateRandomId } from '@/utils';
import { decode } from 'base64-arraybuffer';
import * as FileSystem from 'expo-file-system';
import { compressImage } from '@/utils/common-utils/compressImage';

interface useImageUploadOptions {
  bucketName: string;
  folderPath?: string;
  contentType?: string;
  upsert?: boolean;
}

interface useImageUploadReturn {
  uploadImage: (uri: string) => Promise<string | null>;
  isUploading: boolean;
  error: string;
  clearError: () => void;
  setError: (error: string) => void;
}

export function useImageUpload({
  bucketName,
  folderPath = '',
  contentType = 'image/jpeg',
  upsert = false,
}: useImageUploadOptions): useImageUploadReturn {
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState('');

  const clearError = () => setError('');

  const uploadImage = async (uri: string): Promise<string | null> => {
    try {
      setIsUploading(true);
      setError('');

      const fileName = `${generateRandomId()}.jpg`;
      const filePath = folderPath ? `${folderPath}/${fileName}` : fileName;

      const compressedUri = await compressImage(uri);

      const base64 = await FileSystem.readAsStringAsync(compressedUri, {
        encoding: FileSystem.EncodingType.Base64,
      });

      const arrayBuffer = decode(base64);

      const { error: uploadError } = await supabase.storage
        .from(bucketName)
        .upload(filePath, arrayBuffer, {
          contentType,
          upsert,
        });

      if (uploadError) {
        setError('Upload failed. Please try again.');
        return null;
      }

      const {
        data: { publicUrl },
      } = supabase.storage.from(bucketName).getPublicUrl(filePath);

      return publicUrl;
    } catch (err) {
      setError('Upload failed. Please try again.');
      return null;
    } finally {
      setIsUploading(false);
    }
  };

  return {
    uploadImage,
    isUploading,
    error,
    clearError,
    setError,
  };
}
