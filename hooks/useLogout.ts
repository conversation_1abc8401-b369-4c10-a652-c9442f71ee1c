import { useState } from 'react';
import { supabase } from '@/lib/supabase';
import {
  handleAuthError,
  getAuthErrorMessage,
} from '@/utils/auth-error-handler';

export function useLogout() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  async function logout(): Promise<boolean> {
    setLoading(true);
    setError(null);

    try {
      const { error: signOutError } = await supabase.auth.signOut();

      if (signOutError) {
        await handleAuthError(signOutError, false);
        const errorMessage = signOutError.code
          ? getAuthErrorMessage(signOutError.code)
          : signOutError.message || 'Something went wrong';
        setError(errorMessage);
        return false;
      }

      return true;
    } catch (err: any) {
      await handleAuthError(err, false);
      setError('An unexpected error occurred');
      return false;
    } finally {
      setLoading(false);
    }
  }

  return { logout, loading, error };
}
