// hooks/useSplashAnimation.ts
import { useRef } from 'react';
import { Animated, Easing } from 'react-native';
import * as SplashScreen from 'expo-splash-screen';
import { useSplash } from '@/context/SplashContext';

export function useSplashAnimation() {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const bgColorAnim = useRef(new Animated.Value(0)).current;
  const { splashDone, completeSplash } = useSplash();

  const handleSplashAnimationEnd = async () => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(bgColorAnim, {
        toValue: 1,
        duration: 500,
        easing: Easing.inOut(Easing.ease),
        useNativeDriver: false,
      }),
    ]).start(async () => {
      completeSplash();
      await SplashScreen.hideAsync();
    });
  };

  const backgroundColor = bgColorAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['#1db960', '#ffffff'],
  });

  return {
    splashDone,
    fadeAnim,
    backgroundColor,
    handleSplashAnimationEnd,
  };
}
