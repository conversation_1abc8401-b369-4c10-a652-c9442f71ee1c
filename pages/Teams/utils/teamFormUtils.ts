import { type TeamFormData } from '@/types/teams';

// Generate short name from team name
export const generateShortName = (teamName: string): string => {
  if (!teamName) return '';
  const words = teamName
    .replace(/\b(the|and|of|fc|club|team|united|city|town)\b/gi, '')
    .trim()
    .split(/\s+/)
    .filter((word) => word.length > 0);

  if (words.length === 0) return '';

  if (words.length === 1) {
    return words[0].substring(0, 4).toUpperCase();
  }

  return words
    .slice(0, 4)
    .map((word) => word.charAt(0))
    .join('')
    .toUpperCase();
};

export const getInitialFormData = (team?: any): TeamFormData => ({
  name: team?.name || '',
  short_name: team?.short_name || '',
  logo_url: team?.logo_url || '',
  jersey_color: team?.jersey_color || '',
});

export const validateTeamForm = (
  form: TeamFormData
): Record<string, string> => {
  const errors: Record<string, string> = {};

  if (!form.name || form.name.trim() === '') {
    errors.name = 'Team name is required';
  } else if (form.name.length < 2) {
    errors.name = 'Team name must be at least 2 characters';
  }

  if (!form.short_name || form.short_name.trim() === '') {
    errors.short_name = 'Short name is required';
  } else if (form.short_name.length > 4) {
    errors.short_name = 'Short name must be 4 characters or less';
  }

  return errors;
};
