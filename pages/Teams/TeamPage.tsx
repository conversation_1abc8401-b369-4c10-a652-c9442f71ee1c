import React, { useState, useCallback } from 'react';
import { View, ScrollView } from 'react-native';
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Button, ButtonText, ButtonIcon } from '@/components/ui/button';
import { UsersIcon, ShirtIcon, PaletteIcon } from 'lucide-react-native';
import { type Team } from '@/types/teams';
import { type Player } from '@/types/player';
import { useFocusEffect } from '@react-navigation/native';
import { fetchPlayers } from '@/services/playerService';
import LogoImage from '@/components/k-components/LogoImage';
import { Divider } from '@/components/ui/divider';
import { toast } from '@/toast/toast';

interface TeamPageProps {
  team: Team;
  tournamentId: string;
}

const TeamPage: React.FC<TeamPageProps> = ({ team, tournamentId }) => {
  const [players, setPlayers] = useState<Player[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalCount, setTotalCount] = useState(0);

  const loadPlayers = useCallback(async () => {
    setLoading(true);
    try {
      const { players: fetchedPlayers, count, error } = await fetchPlayers({
        tournamentId,
        teamId: team.id,
        limit: 50, // Load all players for team view
      });

      if (error) {
        toast.error('Failed to load team players');
        return;
      }

      setPlayers(fetchedPlayers || []);
      setTotalCount(count || 0);
    } catch (error) {
      toast.error('Something went wrong');
    } finally {
      setLoading(false);
    }
  }, [team.id, tournamentId]);

  useFocusEffect(
    useCallback(() => {
      loadPlayers();
    }, [loadPlayers])
  );

  const renderTeamHeader = () => (
    <VStack className="items-center py-8 px-4">
      <LogoImage
        logoUrl={team.logo_url}
        fallbackText={team.name}
        width={120}
        height={120}
        borderRadius={60}
        fallBacktextClassName="text-3xl font-urbanistBold"
      />
      
      <Text className="text-3xl font-urbanistBold text-typography-900 mt-4 text-center">
        {team.name}
      </Text>
      
      <Text className="text-lg font-urbanistMedium text-typography-600 mt-1">
        {team.short_name}
      </Text>
    </VStack>
  );

  const renderTeamInfo = () => (
    <VStack className="px-4 py-4 space-y-4">
      <Text className="text-xl font-urbanistBold text-typography-900">
        Team Information
      </Text>
      
      <VStack className="bg-white rounded-lg border border-outline-100 p-4 space-y-3">
        <HStack className="items-center space-x-3">
          <View className="w-10 h-10 bg-primary-100 rounded-full items-center justify-center">
            <ShirtIcon size={20} className="text-primary-600" />
          </View>
          <VStack className="flex-1">
            <Text className="text-sm font-urbanistMedium text-typography-600">
              Short Name
            </Text>
            <Text className="text-base font-urbanistSemiBold text-typography-900">
              {team.short_name}
            </Text>
          </VStack>
        </HStack>
        
        <Divider />
        
        {team.jersey_color && (
          <>
            <HStack className="items-center space-x-3">
              <View className="w-10 h-10 bg-primary-100 rounded-full items-center justify-center">
                <PaletteIcon size={20} className="text-primary-600" />
              </View>
              <VStack className="flex-1">
                <Text className="text-sm font-urbanistMedium text-typography-600">
                  Jersey Color
                </Text>
                <HStack className="items-center space-x-2">
                  <View 
                    className="w-6 h-6 rounded-full border border-outline-200"
                    style={{ backgroundColor: team.jersey_color }}
                  />
                  <Text className="text-base font-urbanistSemiBold text-typography-900">
                    {team.jersey_color}
                  </Text>
                </HStack>
              </VStack>
            </HStack>
            <Divider />
          </>
        )}
        
        <HStack className="items-center space-x-3">
          <View className="w-10 h-10 bg-primary-100 rounded-full items-center justify-center">
            <UsersIcon size={20} className="text-primary-600" />
          </View>
          <VStack className="flex-1">
            <Text className="text-sm font-urbanistMedium text-typography-600">
              Squad Size
            </Text>
            <Text className="text-base font-urbanistSemiBold text-typography-900">
              {totalCount} {totalCount === 1 ? 'Player' : 'Players'}
            </Text>
          </VStack>
        </HStack>
      </VStack>
    </VStack>
  );

  const renderSquadPreview = () => (
    <VStack className="px-4 py-4 space-y-4">
      <HStack className="items-center justify-between">
        <Text className="text-xl font-urbanistBold text-typography-900">
          Squad
        </Text>
        {totalCount > 0 && (
          <Text className="text-sm font-urbanistMedium text-primary-600">
            {totalCount} {totalCount === 1 ? 'Player' : 'Players'}
          </Text>
        )}
      </HStack>
      
      {loading ? (
        <View className="py-8">
          <Text className="text-center text-typography-600">Loading squad...</Text>
        </View>
      ) : players.length > 0 ? (
        <VStack className="bg-white rounded-lg border border-outline-100 p-4 space-y-3">
          {players.slice(0, 5).map((player, index) => (
            <View key={player.id}>
              <HStack className="items-center space-x-3">
                <View className="w-8 h-8 bg-primary-100 rounded-full items-center justify-center">
                  <Text className="text-primary-600 font-urbanistBold text-xs">
                    {player.name.charAt(0).toUpperCase()}
                  </Text>
                </View>
                <VStack className="flex-1">
                  <Text className="text-base font-urbanistMedium text-typography-800">
                    {player.name}
                  </Text>
                  {player.jersey_number && (
                    <Text className="text-sm text-typography-600">
                      Jersey #{player.jersey_number}
                    </Text>
                  )}
                </VStack>
              </HStack>
              {index < Math.min(players.length, 5) - 1 && <Divider className="mt-3" />}
            </View>
          ))}
          
          {players.length > 5 && (
            <>
              <Divider />
              <Text className="text-center text-sm text-typography-600">
                +{players.length - 5} more players
              </Text>
            </>
          )}
        </VStack>
      ) : (
        <View className="bg-white rounded-lg border border-outline-100 p-8">
          <Text className="text-center text-typography-600">
            No players in squad yet
          </Text>
        </View>
      )}
    </VStack>
  );

  return (
    <ScrollView showsVerticalScrollIndicator={false} className="flex-1">
      {renderTeamHeader()}
      {renderTeamInfo()}
      {renderSquadPreview()}
    </ScrollView>
  );
};

export default TeamPage;
