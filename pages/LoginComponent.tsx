import React, { useState } from 'react';
import SCREENS from '@/constants/Screens';
import { Input, InputField, InputIcon, InputSlot } from '@/components/ui/input';
import { Text } from '@/components/ui/text';
import { EyeIcon, EyeOffIcon } from '@/components/ui/icon';
import { VStack } from '@/components/ui/vstack';
import { Pressable } from 'react-native';
import { useLogin } from '@/hooks/useLogin';
import { useRouter } from 'expo-router';
import { CTAButton } from '../components/ui/primaryCTAbutton';
import { CustomInput } from '../components/ui/customInput';
import { Link } from 'expo-router';
import { validateEmail } from '@/utils';
import { ErrorText } from '../components/ui/errortext';
import { toast } from '@/toast/toast';

export function LoginComponent() {
  const { login, loading, error } = useLogin();
  const router = useRouter();

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  const handleLogin = async () => {
    try {
      const success = await login(email, password);
      if (success) {
        router.back();
      }
    } catch (error: any) {
      toast.error('Something went wrong. Please try again.');
    }
  };

  const isFormValid =
    email.trim() !== '' && password.trim() !== '' && !validateEmail(email);

  return (
    <VStack space="xs" className="w-full px-5">
      <Text className="text-3xl font-urbanistBold text-typography-800 text-center pb-3">
        Welcome to Kali
      </Text>
      <Text className="text-xl font-urbanist text-typography-700 text-center">
        Login to continue
      </Text>

      {/* Inputs and Buttons */}
      <VStack space="lg" className="w-full pt-6">
        {/* Email Field */}
        <CustomInput
          label="Email"
          value={email}
          onChangeText={setEmail}
          placeholder="Enter your email"
          type="text"
          validate={validateEmail}
        />

        {/* Password Field */}
        <VStack space="xs" className="w-full">
          <Text className="font-urbanistSemiBold">Password</Text>
          <Input className="w-full " size="lg">
            <InputField
              type={showPassword ? 'text' : 'password'}
              placeholder="Enter your password"
              className="font-urbanist text-base"
              value={password}
              placeholderClassName="text-xs"
              onChangeText={setPassword}
              autoCapitalize="none"
              selectionColor="#1db960"
            />
            <InputSlot
              className="pr-3"
              onTouchStart={() => setShowPassword(!showPassword)}
            >
              <InputIcon size="xl" as={showPassword ? EyeOffIcon : EyeIcon} />
            </InputSlot>
          </Input>

          <Link className="self-end" href={SCREENS.FORGOT_PASSWORD}>
            <Text className="text-primary-0 font-urbanistSemiBold text-sm">
              Forgot Password?
            </Text>
          </Link>
        </VStack>

        <VStack>
          <CTAButton
            onPress={handleLogin}
            loading={loading}
            isFormValid={isFormValid}
            title="Continue"
          />
          <ErrorText message={error} />
        </VStack>

        {/* Divider */}
        <VStack className="items-center pt-6 w-full">
          <Text className="text-typography-500 font-urbanist">
            Don't have an account?
          </Text>
          <Pressable>
            <Link href={SCREENS.SIGNUP}>
              <Text className="text-primary-0 font-urbanistSemiBold text-base">
                Sign Up
              </Text>
            </Link>
          </Pressable>
        </VStack>
      </VStack>
    </VStack>
  );
}
