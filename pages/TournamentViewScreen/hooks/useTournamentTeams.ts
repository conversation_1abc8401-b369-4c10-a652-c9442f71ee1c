import { useState, useCallback, useEffect } from 'react';
import { fetchTeams } from '@/services/teamsService';
import { toast } from '@/toast/toast';

export const useTournamentTeams = (
  tournamentId: string,
  isActionsheetOpen: boolean
) => {
  const [teams, setTeams] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalCount, setTotalCount] = useState(0);

  const loadTeams = async (isInitialLoad = true) => {
    if (isInitialLoad) setLoading(true);
    const {
      teams: fetchedTeams,
      count,
      error,
    } = await fetchTeams({ tournamentId });

    if (error && !isActionsheetOpen) {
      toast.error(error || 'Something went wrong!');
    }

    setTotalCount(count || 0);
    if (isInitialLoad) {
      setTeams(fetchedTeams || []);
      setLoading(false);
    } else {
      setTeams((currentTeams) => {
        const currentIds = new Set(currentTeams.map((t) => t.id));
        const newTeams = (fetchedTeams || []).filter(
          (t) => !currentIds.has(t.id)
        );
        return [...newTeams, ...currentTeams];
      });
    }
  };

  const addTeamOptimistically = useCallback((newTeam: any) => {
    const tempId = `temp-${Date.now()}`;
    const teamWithTempId = { ...newTeam, id: tempId, isOptimistic: true };
    setTeams((current) => [teamWithTempId, ...current]);
    setTotalCount((c) => c + 1);
    return tempId;
  }, []);

  const removeOptimisticTeam = useCallback((tempId: string) => {
    setTeams((current) => current.filter((t) => t.id !== tempId));
    setTotalCount((c) => c - 1);
  }, []);

  const confirmOptimisticTeam = useCallback(
    (tempId: string, actualTeam: any) => {
      setTeams((current) =>
        current.map((t) => (t.id === tempId ? actualTeam : t))
      );
    },
    []
  );

  const handleTeamAdded = async (newTeam?: any) => {
    if (newTeam) {
      const tempId = addTeamOptimistically(newTeam);
      // Note: createTeam would be called from the form component
      // This is just for optimistic updates
    } else {
      await loadTeams(false);
    }
  };

  useEffect(() => {
    loadTeams(true);
  }, [tournamentId]);

  return {
    teams,
    totalCount,
    loading,
    handleTeamAdded,
    reloadTeams: loadTeams,
  };
};
