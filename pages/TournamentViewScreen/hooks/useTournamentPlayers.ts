import { useState, useCallback, useEffect } from 'react';
import { fetchPlayers, createPlayer } from '@/services/playerService';
import { toast } from '@/toast/toast';

export const useTournamentPlayers = (
  tournamentId: string,
  isActionsheetOpen: boolean
) => {
  const [players, setPlayers] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalCount, setTotalCount] = useState(0);

  const loadPlayers = async (isInitialLoad = true) => {
    if (isInitialLoad) setLoading(true);
    const {
      players: fetchedPlayers,
      count,
      error,
    } = await fetchPlayers({ tournamentId });

    if (error && !isActionsheetOpen) {
      toast.error(error || 'Something went wrong!');
    }

    setTotalCount(count || 0);
    if (isInitialLoad) {
      setPlayers(fetchedPlayers || []);
      setLoading(false);
    } else {
      setPlayers((currentPlayers) => {
        const currentIds = new Set(currentPlayers.map((p) => p.id));
        const newPlayers = (fetchedPlayers || []).filter(
          (p) => !currentIds.has(p.id)
        );
        return [...newPlayers, ...currentPlayers];
      });
    }
  };

  const addPlayerOptimistically = useCallback((newPlayer: any) => {
    const tempId = `temp-${Date.now()}`;
    const playerWithTempId = { ...newPlayer, id: tempId, isOptimistic: true };
    setPlayers((current) => [playerWithTempId, ...current]);
    setTotalCount((c) => c + 1);
    return tempId;
  }, []);

  const removeOptimisticPlayer = useCallback((tempId: string) => {
    setPlayers((current) => current.filter((p) => p.id !== tempId));
    setTotalCount((c) => c - 1);
  }, []);

  const confirmOptimisticPlayer = useCallback(
    (tempId: string, actualPlayer: any) => {
      setPlayers((current) =>
        current.map((p) => (p.id === tempId ? actualPlayer : p))
      );
    },
    []
  );

  const handlePlayerAdded = async (newPlayer?: any) => {
    if (newPlayer) {
      const tempId = addPlayerOptimistically(newPlayer);
      const result = await createPlayer(newPlayer);

      if (result?.error) {
        removeOptimisticPlayer(tempId);
        if (!isActionsheetOpen)
          toast.error(result.error || 'Something went wrong!');
      } else {
        confirmOptimisticPlayer(tempId, result);
      }
    } else {
      await loadPlayers(false);
    }
  };

  useEffect(() => {
    loadPlayers(true);
  }, [tournamentId]);

  return {
    players,
    totalCount,
    loading,
    handlePlayerAdded,
    reloadPlayers: loadPlayers,
  };
};
