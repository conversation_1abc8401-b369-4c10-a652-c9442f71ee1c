import React from 'react';
import { View, Pressable } from 'react-native';
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import { Button, ButtonText, ButtonIcon } from '@/components/ui/button';
import { PlusIcon } from 'lucide-react-native';
import { useRouter } from 'expo-router';
import NoDataFound from '@/components/k-components/NoDataFound';
import FullscreenLoader from '@/components/k-components/FullscreenLoader';
import EventsSection from '@/components/k-components/EventsSection';
import { TeamListCard } from '../Teams/components/TeamListCard';
import SCREENS from '@/constants/Screens';
import { useTournamentTeams } from './hooks/useTournamentTeams';
import { triggerHapticFeedback } from '@/utils';
import { type Tournament } from './types';

interface TournamentTeamsProps {
  tournament: Tournament;
}

const AddTeamCTA = ({ onPress }: { onPress: () => void }) => (
  <Pressable
    onPress={() => {
      triggerHapticFeedback();
      onPress();
    }}
    className="flex-row items-center justify-start mt-4 px-2"
  >
    <View className="w-11 h-11 rounded-full bg-primary-0 items-center justify-center mr-3">
      <PlusIcon color="white" size={20} />
    </View>
    <Text className="text-primary-0 font-urbanistSemiBold">Add Team</Text>
  </Pressable>
);

const TournamentTeams: React.FC<TournamentTeamsProps> = ({ tournament }) => {
  const router = useRouter();
  const { teams, totalCount, loading } = useTournamentTeams(
    tournament.id,
    false
  );

  const handleAddTeam = () => {
    router.push({
      pathname: SCREENS.TEAMS_CREATE,
      params: {
        'tournament-id': tournament.id,
      },
    });
  };

  if (loading) return <FullscreenLoader position="top" />;

  return (
    <VStack className="px-4 space-y-4 pb-6">
      {teams.length === 0 ? (
        <NoDataFound
          title="No Teams Added Yet"
          subtitle="Add teams to get started with your tournament."
          action={
            <Button className="bg-primary-0" onPress={handleAddTeam}>
              <ButtonIcon as={PlusIcon} />
              <ButtonText className="font-urbanistSemiBold">
                Add Team
              </ButtonText>
            </Button>
          }
        />
      ) : (
        <View>
          <EventsSection
            title={`Teams (${totalCount})`}
            events={teams}
            eventType="teams"
            animated
            visibleCount={8}
            extraParams={{ tournamentId: tournament.id }}
            renderCard={(team) => (
              <TeamListCard team={team} tournamentId={tournament.id} />
            )}
          />
          <AddTeamCTA onPress={handleAddTeam} />
        </View>
      )}
    </VStack>
  );
};

export default TournamentTeams;
