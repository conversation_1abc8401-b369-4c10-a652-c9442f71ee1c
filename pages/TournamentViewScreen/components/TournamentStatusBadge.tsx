import React, { useEffect } from 'react';
import { View, Animated, Easing } from 'react-native';
import { Text } from '@/components/ui/text';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import duration from 'dayjs/plugin/duration';
import { getEventStatus } from '@/utils/tournament-utils';

dayjs.extend(relativeTime);
dayjs.extend(duration);

interface Props {
  start_date?: string;
  end_date?: string;
}

const TournamentStatusBadge = ({ start_date, end_date }: Props) => {
  const now = dayjs();
  const start = start_date ? dayjs(start_date) : null;
  const end = end_date ? dayjs(end_date) : null;

  const status =
    start && end
      ? getEventStatus(start.format('YYYY-MM-DD'), end.format('YYYY-MM-DD'))
      : null;

  const getDayCount = () => {
    if (!start || !end) return '';
    const dayNum = now.diff(start, 'day') + 1;
    const totalDays = end.diff(start, 'day') + 1;
    return `Day ${dayNum} of ${totalDays}`;
  };

  const getUpcomingText = () => {
    if (!start) return '';
    const now = dayjs();
    const s = dayjs(start);

    if (s.isBefore(now)) return 'Already started';
    return `Upcoming ${now.to(s)}`;
  };

  const getCompletedText = () => {
    if (!end) return '';
    return `Completed ${end.fromNow()}`;
  };

  const pulse = new Animated.Value(1);

  useEffect(() => {
    if (status === 'live') {
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulse, {
            toValue: 1.4,
            duration: 600,
            easing: Easing.out(Easing.ease),
            useNativeDriver: true,
          }),
          Animated.timing(pulse, {
            toValue: 1,
            duration: 600,
            easing: Easing.in(Easing.ease),
            useNativeDriver: true,
          }),
        ])
      ).start();
    }
  }, [status]);

  return (
    <View className="flex-row items-center gap-2 my-2">
      {status === 'live' && (
        <View className="flex-row w-full p-2 gap-2 bg-red-600/10 border border-red-600 rounded-lg items-center justify-center">
          <View className="items-center justify-center">
            <Animated.View
              style={{
                width: 8,
                height: 8,
                borderRadius: 6,
                backgroundColor: '#EF4444',
                transform: [{ scale: pulse }],
              }}
            />
          </View>

          <Text className="text-base text-red-600 font-urbanistBold">
            LIVE · {getDayCount()}
          </Text>
        </View>
      )}

      {status === 'upcoming' && (
        <View className="p-2 w-full bg-yellow-600/10 border border-yellow-600 rounded-lg flex items-center justify-center">
          <Text className="text-base text-yellow-600 font-urbanistMedium">
            {getUpcomingText()}
          </Text>
        </View>
      )}

      {status === 'completed' && (
        <View className="p-2 w-full bg-gray-600/10 border border-gray-500 rounded-lg flex items-center justify-center">
          <Text className="text-base text-gray-700 font-urbanistMedium">
            {getCompletedText()}
          </Text>
        </View>
      )}
    </View>
  );
};

export default TournamentStatusBadge;
