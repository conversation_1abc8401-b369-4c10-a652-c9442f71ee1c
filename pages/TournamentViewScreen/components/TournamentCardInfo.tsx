import React from 'react';
import { View } from 'react-native';
import { Text } from '@/components/ui/text';
import { Icon } from '@/components/ui/icon';
import {
  CalendarDays,
  MapPin,
  Globe,
  GlobeLockIcon,
} from 'lucide-react-native';
import { type Tournament } from '../types';
import dayjs from 'dayjs';
import InfoListItem from '@/components/util-components/InfoListItem';
import { getSportLabel } from '@/utils/sports-utils';

interface Props {
  tournament: Tournament;
}

function formatVenue(venue?: string, displayName?: string): string | undefined {
  if (!venue && !displayName) return undefined;
  if (venue && displayName) return `${venue}, ${displayName}`;
  return venue || displayName || undefined;
}

const TournamentCardInfo = ({ tournament }: Props) => {
  const formattedDates =
    tournament.start_date && tournament.end_date
      ? `${dayjs(tournament.start_date).format('DD MMM YYYY')} - ${dayjs(
          tournament.end_date
        ).format('DD MMM YYYY')}`
      : undefined;
  return (
    <View className="flex-1 bg-white border border-gray-200 rounded-lg p-4 shadow-sm mt-2">
      <View className="flex-col gap-2">
        <View className="flex-row items-center justify-between">
          <View className="flex-col">
            <Text
              className="font-urbanistExtraBold text-[8px] text-gray-400 tracking-widest"
              numberOfLines={1}
            >
              SPORT
            </Text>
            <Text
              className="font-urbanistBold text-xl leading-tight"
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {getSportLabel(tournament.sport_type)}
            </Text>
          </View>
          <Icon
            as={tournament.public ? Globe : GlobeLockIcon}
            size="md"
            className={`${
              tournament.public ? 'text-primary-0' : 'text-gray-400'
            }`}
          />
        </View>

        <View className="border-t border-dashed border-gray-300" />
        <View className="flex-col gap-0.5">
          <InfoListItem icon={CalendarDays} value={formattedDates} />
          <InfoListItem
            icon={MapPin}
            numberOfLines={2}
            value={formatVenue(tournament.venue, tournament.display_name)}
          />
        </View>
      </View>
    </View>
  );
};

export default TournamentCardInfo;
