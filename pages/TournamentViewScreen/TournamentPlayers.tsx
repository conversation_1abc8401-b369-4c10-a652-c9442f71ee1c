import React, { useState } from 'react';
import { View, Pressable } from 'react-native';
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import { Button, ButtonText, ButtonIcon } from '@/components/ui/button';
import { PlusIcon } from 'lucide-react-native';
import { type Tournament } from './types';
import NoDataFound from '@/components/k-components/NoDataFound';
import FullscreenLoader from '@/components/k-components/FullscreenLoader';
import EventsSection from '@/components/k-components/EventsSection';
import { PlayerListCard } from '../Players/components/PlayerListCard';
import {
  Actionsheet,
  ActionsheetBackdrop,
  ActionsheetContent,
} from '@/components/ui/actionsheet';
import AddPlayer from '../Players/AddPlayer';
import { ActionsheetDragIndicator } from '@/components/ui/select/select-actionsheet';
import { useTournamentPlayers } from './hooks/useTournamentPlayers';
import { triggerHapticFeedback } from '@/utils';

interface TournamentPlayersProps {
  tournament: Tournament;
}

const AddPlayerCTA = ({ onPress }: { onPress: () => void }) => (
  <Pressable
    onPress={() => {
      triggerHapticFeedback();
      onPress();
    }}
    className="flex-row items-center justify-start mt-4 px-2"
  >
    <View className="w-11 h-11 rounded-full bg-primary-0 items-center justify-center mr-3">
      <PlusIcon color="white" size={20} />
    </View>
    <Text className="text-primary-0 font-urbanistSemiBold">Add Player</Text>
  </Pressable>
);

const TournamentPlayers: React.FC<TournamentPlayersProps> = ({
  tournament,
}) => {
  const [open, setOpen] = useState(false);
  const { players, totalCount, loading, handlePlayerAdded } =
    useTournamentPlayers(tournament.id, open);

  if (loading) return <FullscreenLoader position="top" />;

  return (
    <VStack className="px-4 space-y-4 pb-6">
      {players.length === 0 ? (
        <NoDataFound
          title="No Players Yet"
          subtitle="Add players to build your tournament roster."
          action={
            <Button className="bg-primary-0" onPress={() => setOpen(true)}>
              <ButtonIcon as={PlusIcon} />
              <ButtonText className="font-urbanistSemiBold">
                Add Player
              </ButtonText>
            </Button>
          }
        />
      ) : (
        <View>
          <EventsSection
            title={`Players (${totalCount})`}
            events={players}
            eventType="players"
            animated
            visibleCount={8}
            extraParams={{ tournamentId: tournament.id }}
            renderCard={(player) => <PlayerListCard player={player} />}
          />
          <AddPlayerCTA onPress={() => setOpen(true)} />
        </View>
      )}

      <Actionsheet isOpen={open} onClose={() => setOpen(false)}>
        <ActionsheetBackdrop />
        <ActionsheetContent>
          <ActionsheetDragIndicator />
          <AddPlayer
            tournament={tournament}
            onClose={async () => {
              setOpen(false);
              await handlePlayerAdded();
            }}
          />
        </ActionsheetContent>
      </Actionsheet>
    </VStack>
  );
};

export default TournamentPlayers;
