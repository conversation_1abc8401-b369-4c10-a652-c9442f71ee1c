import React from 'react';
import { View } from 'react-native';
import { Text } from '@/components/ui/text';
import LogoImage from '@/components/k-components/LogoImage';
import { Divider } from '@/components/ui/divider';
import { Icon } from '@/components/ui/icon';
import { Medal, MapPinIcon } from 'lucide-react-native';
import { type Tournament } from './types';
import { TabItem } from '@/types/tab';
import { useRecoilState } from 'recoil';
import { tournamentTabState } from '@/atoms/tournamentTabs';
import CollapsibleTabView from '@/components/CollapsibleTabView';
import TournamentOverview from './TournamentOverview';
import TournamentTeams from './TournamentTeams';
import TournamentPlayers from './TournamentPlayers';
import { getSportLabel } from '@/utils/sports-utils';

interface TournamentViewScreenProps {
  tournament: Tournament;
}

const HEADER_HEIGHT = 200;

const tournamentTabs: TabItem<Tournament & { isTeamNameOptional?: boolean }>[] =
  [
    {
      id: 'overview',
      name: 'Overview',
      render: (tournament: Tournament) => (
        <TournamentOverview tournament={tournament} />
      ),
    },
    {
      id: 'matches',
      name: 'Matches',
      render: () => (
        <Text className="text-base text-typography-600">No matches yet.</Text>
      ),
    },
    {
      id: 'teams',
      name: 'Teams',
      render: (tournament: Tournament & { isTeamNameOptional?: boolean }) => (
        <TournamentTeams tournament={tournament} />
      ),
    },
    {
      id: 'players',
      name: 'Players',
      render: (tournament: Tournament) => (
        <TournamentPlayers tournament={tournament} />
      ),
    },
    {
      id: 'stats',
      name: 'Stats',
      render: () => (
        <Text className="text-base text-typography-600">Stats go here</Text>
      ),
    },
  ];

const TournamentHeader = ({ tournament }: { tournament: Tournament }) => (
  <View className="flex flex-col items-center justify-center bg-background-0 py-8">
    <LogoImage
      className="mb-2"
      logoUrl={tournament.logo_url}
      fallbackText={tournament.name}
    />
    <Text
      className="text-typography-700 font-urbanistBold text-2xl"
      numberOfLines={1}
      ellipsizeMode="tail"
    >
      {tournament.name}
    </Text>

    {(tournament.sport_type || tournament.venue) && (
      <>
        <Divider className="w-1/2 my-2" />
        <View className="flex flex-row items-center gap-1">
          {tournament.sport_type && (
            <View className="flex-row items-center gap-1">
              <Icon as={Medal} size="xs" className="text-typography-600" />
              <Text className="text-sm text-typography-600 font-urbanistMedium">
                {getSportLabel(tournament.sport_type)}
              </Text>
            </View>
          )}
          {tournament.sport_type && tournament.venue && (
            <View className="w-1 h-1 rounded-full bg-typography-600 mx-2 mt-1" />
          )}
          {tournament.venue && (
            <View className="flex-row items-center gap-1">
              <Icon as={MapPinIcon} size="xs" className="text-typography-600" />
              <Text className="text-sm text-typography-600 font-urbanistMedium">
                {tournament.venue}
              </Text>
            </View>
          )}
        </View>
      </>
    )}
  </View>
);

export default function TournamentViewScreen({
  tournament,
}: TournamentViewScreenProps) {
  const [tabState, setTabState] = useRecoilState(tournamentTabState);
  const selectedTab = tabState[tournament.id] || 'overview';

  const handleTabChange = ({ tabName }: { tabName: string }) => {
    setTabState((prev) => ({
      ...prev,
      [tournament.id]: tabName,
    }));
  };

  const format = tournament.tournament_rules?.game_format;

  const tabFilter = (
    tab: TabItem<Tournament & { isTeamNameOptional?: boolean }>
  ) => {
    if (tab.id === 'teams' && format === 'singles') return false;
    return true;
  };

  const tournamentData = {
    ...tournament,
    isTeamNameOptional: format === 'doubles',
  };

  const tabBarConfig = {
    scrollEnabled: true,
    minWidth: 110,
    paddingVertical: 8,
    paddingHorizontal: 20,
    tabStyle: {
      minWidth: 300,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
    },
    contentContainerStyle: { backgroundColor: '#fff' },
  };

  return (
    <CollapsibleTabView
      data={tournamentData}
      tabs={tournamentTabs}
      lazy={true}
      tabBarConfig={tabBarConfig}
      initialTabName={selectedTab}
      onTabChange={handleTabChange}
      renderHeader={() => <TournamentHeader tournament={tournament} />}
      headerHeight={HEADER_HEIGHT}
      tabFilter={tabFilter}
    />
  );
}
