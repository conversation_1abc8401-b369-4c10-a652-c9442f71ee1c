import React from 'react';
import { View } from 'react-native';
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import { Icon } from '@/components/ui/icon';
import { BarChart3, TrendingUp, Award } from 'lucide-react-native';
import { type Player } from '@/types/player';

interface PlayerStatsProps {
  player: Player;
}

export default function PlayerStats({ player }: PlayerStatsProps) {
  return (
    <VStack className="px-4 space-y-6 pb-6 flex-1 gap-5 py-6">
      {/* Stats Overview */}
      <View className="bg-white rounded-lg p-4 shadow-sm border border-outline-100">
        <View className="flex-row items-center gap-2 mb-4">
          <Icon as={BarChart3} size="sm" className="text-primary-0" />
          <Text className="text-lg font-urbanistSemiBold text-typography-900">
            Performance Statistics
          </Text>
        </View>

        <Text className="text-base text-typography-600 text-center py-8 font-urbanist">
          Player statistics will be displayed here once matches are played.
        </Text>
      </View>

      {/* Performance Metrics */}
      <View className="bg-white rounded-lg p-4 shadow-sm border border-outline-100">
        <View className="flex-row items-center gap-2 mb-4">
          <Icon as={TrendingUp} size="sm" className="text-primary-0" />
          <Text className="text-lg font-urbanistSemiBold text-typography-900">
            Performance Metrics
          </Text>
        </View>

        <VStack className="space-y-4 gap-2">
          <View className="flex-row justify-between items-center p-3 bg-background-50 rounded-lg">
            <Text className="text-sm font-urbanistMedium text-typography-600">
              Matches Played
            </Text>
            <Text className="text-lg font-urbanistSemiBold text-typography-900">
              0
            </Text>
          </View>

          <View className="flex-row justify-between items-center p-3 bg-background-50 rounded-lg">
            <Text className="text-sm font-urbanistMedium text-typography-600">
              Wins
            </Text>
            <Text className="text-lg font-urbanistSemiBold text-typography-900">
              0
            </Text>
          </View>

          <View className="flex-row justify-between items-center p-3 bg-background-50 rounded-lg">
            <Text className="text-sm font-urbanistMedium text-typography-600">
              Win Rate
            </Text>
            <Text className="text-lg font-urbanistSemiBold text-typography-900">
              0%
            </Text>
          </View>
        </VStack>
      </View>

      {/* Achievements */}
      <View className="bg-white rounded-lg p-4 shadow-sm border border-outline-100">
        <View className="flex-row items-center gap-2 mb-4">
          <Icon as={Award} size="sm" className="text-primary-0" />
          <Text className="text-lg font-urbanistSemiBold text-typography-900">
            Achievements
          </Text>
        </View>

        <Text className="text-base text-typography-600 text-center py-8 font-urbanist">
          Player achievements and awards will be displayed here.
        </Text>
      </View>
    </VStack>
  );
}
