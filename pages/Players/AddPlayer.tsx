import React, { useState } from 'react';
import { createPlayer } from '@/services/playerService';
import { type Tournament } from '@/pages/TournamentViewScreen/types';
import PlayerForm from '@/components/PlayerForm';
import { type PlayerFormData } from '@/types/player';

interface AddPlayerProps {
  tournament: Tournament;
  teamId?: string;
  onClose: () => void;
}

const AddPlayer: React.FC<AddPlayerProps> = ({
  tournament,
  teamId,
  onClose,
}) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (formData: PlayerFormData) => {
    setIsLoading(true);

    try {
      const { success, error: dbError } = await createPlayer({
        tournament_id: tournament.id,
        name: formData.name,
        ...(teamId ? { team_id: teamId } : {}),
        jersey_number: formData.jersey_number,
        email: formData.email,
      });

      if (!success) {
        throw new Error(dbError || 'Failed to add player, Try Again!');
      }

      onClose();
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <PlayerForm
      onSubmit={handleSubmit}
      submitButtonText="Add Player"
      isLoading={isLoading}
    />
  );
};

export default AddPlayer;
