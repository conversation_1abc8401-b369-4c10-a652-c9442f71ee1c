import React from 'react';
import { type Player } from '@/types/player';
import { TabItem } from '@/types/tab';
import CollapsibleTabView from '@/components/CollapsibleTabView';
import PlayerOverview from './PlayerOverview';
import PlayerStats from './PlayerStats';
import PlayerMatches from './PlayerMatches';

interface PlayerPageProps {
  player: Player;
}

const playerTabs: TabItem<Player>[] = [
  {
    id: 'overview',
    name: 'Overview',
    render: (player: Player) => <PlayerOverview player={player} />,
  },
  {
    id: 'stats',
    name: 'Stats',
    render: (player: Player) => <PlayerStats player={player} />,
  },
  {
    id: 'matches',
    name: 'Matches',
    render: (player: Player) => <PlayerMatches player={player} />,
  },
];

export default function PlayerPage({ player }: PlayerPageProps) {
  return <CollapsibleTabView data={player} tabs={playerTabs} lazy={false} />;
}
