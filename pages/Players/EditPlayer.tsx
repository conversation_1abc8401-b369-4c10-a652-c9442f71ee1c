import React, { useState } from 'react';
import { updatePlayer } from '@/services/playerService';
import { type Player, type PlayerFormData } from '@/types/player';
import PlayerForm from '@/components/PlayerForm';

interface EditPlayerProps {
  player: Player;
  onClose: () => void;
}

const EditPlayer: React.FC<EditPlayerProps> = ({ player, onClose }) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (formData: PlayerFormData) => {
    setIsLoading(true);

    try {
      const { success, error: dbError } = await updatePlayer(player.id, {
        name: formData.name,
        jersey_number: formData.jersey_number,
        email: formData.email,
      });

      if (!success) {
        throw new Error(dbError || 'Failed to update player, Try Again!');
      }

      onClose();
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <PlayerForm
      initialData={player}
      onSubmit={handleSubmit}
      submitButtonText="Update Player"
      isLoading={isLoading}
    />
  );
};

export default EditPlayer;
