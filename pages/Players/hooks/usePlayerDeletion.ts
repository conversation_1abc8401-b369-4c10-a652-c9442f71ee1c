import { useState } from 'react';
import { useRouter } from 'expo-router';
import { deletePlayer } from '@/services/playerService';
import { toast } from '@/toast/toast';
import { type Player } from '@/types/player';

export function usePlayerDeletion() {
  const [deleting, setDeleting] = useState(false);
  const router = useRouter();

  const handleDeletePlayer = async (player: Player) => {
    if (!player) return { success: false, error: 'No player provided' };

    setDeleting(true);
    
    try {
      const result = await deletePlayer(player.id);
      
      if (result.success) {
        toast.success('Player removed successfully');
        router.back();
        return { success: true };
      } else {
        toast.error(result.error || 'Failed to remove player');
        return { success: false, error: result.error };
      }
    } catch (error) {
      const errorMessage = 'An unexpected error occurred';
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setDeleting(false);
    }
  };

  return {
    deleting,
    handleDeletePlayer,
  };
}
