import { validateEmail } from '@/utils';
import { type PlayerFormData } from '@/types/player';

export const getInitialFormData = (player?: any): PlayerFormData => ({
  name: player?.name || '',
  jersey_number: player?.jersey_number || '',
  email: player?.email || '',
});

export const validatePlayerForm = (
  form: PlayerFormData
): Record<string, string> => {
  const errors: Record<string, string> = {};

  if (!form.name) {
    errors.name = 'Name is required';
  }

  if (form.email) {
    const emailError = validateEmail(form.email);
    if (emailError) {
      errors.email = emailError;
    }
  }

  return errors;
};
