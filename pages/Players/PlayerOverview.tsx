import React, { useEffect, useRef } from 'react';
import { Animated, View } from 'react-native';
import { VStack } from '@/components/ui/vstack';
import { type Player } from '@/types/player';
import LogoImage from '@/components/k-components/LogoImage';
import { Text } from '@/components/ui/text';
import ListInfoCard from '@/components/util-components/ListInfoCard';
import { MailIcon, PhoneIcon } from 'lucide-react-native';

interface PlayerOverviewProps {
  player: Player;
}

export default function PlayerOverview({ player }: PlayerOverviewProps) {
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 400,
      useNativeDriver: true,
    }).start();
  }, []);

  const shouldBreak = player.name.length > 12 && player.name.includes(' ');

  const formattedName = shouldBreak
    ? player.name.replace(/ (.+)/, '\n$1')
    : player.name;

  const infoItems = [
    {
      icon: MailIcon,
      value: player.email || 'N/A',
    },
    {
      icon: PhoneIcon,
      value: player.phone || 'N/A',
    },
  ];

  return (
    <VStack className="pb-[50px]">
      <View className="relative items-center">
        <View className="flex-1 bg-primary-0 h-44 w-full" />
        <Animated.View
          style={{
            opacity: fadeAnim,
            transform: [{ translateY: 74 }],
            position: 'absolute',
            bottom: 0,
            left: '50%',
            marginLeft: -74,
          }}
        >
          <View className="items-center justify-center rounded-full bg-white p-1">
            <LogoImage
              fallbackText={player.name}
              width={140}
              height={140}
              borderRadius={100}
              fallBacktextClassName="text-4xl font-urbanistExtraBold"
            />
          </View>
        </Animated.View>
      </View>
      <View className="px-4 space-y-6 pb-6 gap-5 py-6 mt-20 flex-1 items-center">
        <Text
          numberOfLines={2}
          ellipsizeMode="tail"
          className="font-urbanistExtraBold text-6xl text-center pt-4 px-5"
        >
          {formattedName}
        </Text>
      </View>
      {player.jersey_number && (
        <Text className="text-center text-lg font-urbanistMedium text-typography-600">
          Jersey #{player.jersey_number}
        </Text>
      )}
      <View className="px-5 pt-6">
        <ListInfoCard title="Player Info" items={infoItems} />
      </View>
    </VStack>
  );
}
