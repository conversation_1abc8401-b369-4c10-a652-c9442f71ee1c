import React from 'react';
import { Mo<PERSON>, ModalBackdrop, ModalContent } from '@/components/ui/modal';
import ConfirmationPrompt from '@/components/k-components/ConfirmationPrompt';
import { usePlayerDeletion } from '@/pages/Players/hooks/usePlayerDeletion';
import { type Player } from '@/types/player';

interface PlayerDeleteConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  player: Player | null;
}

const PlayerDeleteConfirmationModal: React.FC<
  PlayerDeleteConfirmationModalProps
> = ({ isOpen, onClose, player }) => {
  const { deleting, handleDeletePlayer } = usePlayerDeletion();

  const onDeleteConfirm = async () => {
    if (!player) return;

    const result = await handleDeletePlayer(player);
    if (result.success) {
      onClose();
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <ModalBackdrop />
      <ModalContent className="p-6">
        <ConfirmationPrompt
          heading="Remove Player?"
          description="This action cannot be undone. Removing this player will remove all associated data."
          primaryText="Remove"
          secondaryText="Cancel"
          loading={deleting}
          onPrimaryPress={onDeleteConfirm}
          onSecondaryPress={onClose}
          type="error"
        />
      </ModalContent>
    </Modal>
  );
};

export default PlayerDeleteConfirmationModal;
