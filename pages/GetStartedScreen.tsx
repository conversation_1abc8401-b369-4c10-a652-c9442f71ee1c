import { View, Text, Image, ScrollView } from 'react-native';
import { NavLayout } from '@/components/NavLayout';
import { Button, ButtonText } from '@/components/ui/button';

type Step = {
  id: number;
  title: string;
  description: string;
  image: any;
};

type GetStartedProps = {
  steps: Step[];
  onCTAClick: () => void;
  ctaText?: string;
};

export default function GetStartedScreen({
  steps,
  onCTAClick,
  ctaText = 'Get Started',
}: GetStartedProps) {
  return (
    <NavLayout title="" className="px-7">
      <View className="flex-1">
        {/* Scrollable Steps */}
        <ScrollView
          contentContainerStyle={{ paddingBottom: 24 }}
          showsVerticalScrollIndicator={false}
        >
          <Text className="text-3xl font-urbanistExtraBold text-typography-800 mb-16 mt-2">
            It’s easy to get started
          </Text>

          {steps.map((step) => (
            <View key={step.id} className="flex-row items-start mb-8">
              <View className="w-10">
                <Text className="text-xl font-urbanistBold text-typography-800">
                  {step.id}
                </Text>
              </View>

              <View className="flex-1 mr-3">
                <Text className="text-lg font-urbanistBold text-typography-800 mb-1">
                  {step.title}
                </Text>
                <Text className="text-typography-600 font-urbanist text-base mb-3">
                  {step.description}
                </Text>
              </View>

              <Image
                source={step.image}
                className="w-16 h-16 self-center"
                resizeMode="contain"
              />
            </View>
          ))}
        </ScrollView>

        {/* Fixed Bottom Button */}
        <View className="py-4">
          <Button
            onPress={onCTAClick}
            size="xl"
            className="bg-primary-0 rounded-md items-center"
          >
            <ButtonText className="text-white font-urbanistSemiBold text-lg">
              {ctaText}
            </ButtonText>
          </Button>
        </View>
      </View>
    </NavLayout>
  );
}
