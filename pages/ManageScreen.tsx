import React, { useCallback, useState } from 'react';
import { View, Text, ActivityIndicator } from 'react-native';
import NoDataFound from '@/components/k-components/NoDataFound';
import { useFocusEffect } from 'expo-router';
import { fetchUserTournaments } from '@/services/tournamentService';
import EventsSection from '@/components/k-components/EventsSection';
import EventCard from '../components/k-components/EventCard';
import SCREENS from '@/constants/Screens';
import { getSportLabel } from '@/utils/sports-utils';

export default function ManageScreen() {
  const [tournaments, setTournaments] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useFocusEffect(
    useCallback(() => {
      const loadTournaments = async () => {
        const { data, success } = await fetchUserTournaments({
          limit: 4,
          page: 1,
        });
        if (success) {
          setTournaments(data || []);
        }
        setLoading(false);
      };
      loadTournaments();
    }, [])
  );

  return (
    <View className="flex-1 bg-white p-1 mt-5">
      <View>
        <Text className="text-2xl font-urbanistBold text-typography-900">
          Control Center
        </Text>
        <Text className="text-sm font-urbanist mt-1 mb-6 text-typography-700">
          Create, edit, and manage your tournaments and matches easily.
        </Text>
      </View>

      <View className="mb-10">
        {loading ? (
          <View className="flex-1 justify-center items-center">
            <ActivityIndicator size="large" color="#1DB960" />
          </View>
        ) : tournaments.length > 0 ? (
          <View className="mb-4 flex flex-col">
            <EventsSection
              title="Your Tournaments"
              events={tournaments}
              eventType="tournament"
              renderCard={(event) => (
                <EventCard
                  name={event.name}
                  logo={event.logo_url}
                  venue={event.venue}
                  type={getSportLabel(event.sport_type)}
                  status={event.status}
                  routeOnPress={{
                    pathname: SCREENS.TOURNAMENT_VIEW,
                    params: {
                      'tournament-id': event.id,
                    },
                  }}
                />
              )}
            />
          </View>
        ) : (
          <NoDataFound
            title="Nothing here yet!"
            subtitle="Create your first tournament or match to get started."
          />
        )}
      </View>
    </View>
  );
}
