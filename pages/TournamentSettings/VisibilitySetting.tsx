import React from 'react';
import { View } from 'react-native';
import { Text } from '@/components/ui/text';
import { Switch } from '@/components/ui/switch';
import { HStack } from '@/components/ui/hstack';
import colors from 'tailwindcss/colors';
import { triggerHapticFeedback } from '@/utils';

type Props = {
  value?: any;
  onSave?: (v: any) => void;
  onCancel?: () => void;
};

export default function VisibilitySetting({ value, onSave }: Props) {
  const handleToggle = () => {
    triggerHapticFeedback();
    onSave?.({ public: !value.public });
  };
  return (
    <View className="w-full flex flex-col gap-2 py-2">
      <HStack className="w-full justify-between items-center px-2 py-5 rounded-xl bg-white border border-gray-200 shadow-sm">
        <View className="flex-1 px-3">
          <Text className="text-lg font-urbanistSemiBold text-typography-800">
            Public Visibility
          </Text>
          <Text className="text-sm text-typography-600 font-urbanist pt-1">
            When enabled, this tournament will be visible to everyone in the
            app.
          </Text>
        </View>

        <Switch
          size="md"
          className="items-start flex"
          value={value.public}
          onToggle={handleToggle}
          trackColor={{ false: '#D1D5DB', true: '#C6F6D5' }}
          thumbColor={value ? '#1DB960' : '#F9FAFB'}
          ios_backgroundColor={colors.gray[300]}
        />
      </HStack>
      <View>
        <Text className="text-xs text-gray-500 font-urbanist">
          Pro tip: Public tournaments appear in global search.
        </Text>
      </View>
    </View>
  );
}
