import React, { useState, useCallback, useEffect } from 'react';
import { View } from 'react-native';
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import { Button } from '@/components/ui/button';
import FormField, { FieldType } from '@/components/k-components/FormField';
import { FieldConfig } from '@/components/k-components/FormField';
import {
  validateTournamentInfoForm,
  type TournamentInfoFormData,
  validateTournamentName,
} from '@/utils/tournament-utils/tournamentInfoFormUtils';
import { CTAButton } from '@/components/ui/primaryCTAbutton';
import { toast } from '@/toast/toast';

type Props = {
  value?: TournamentInfoFormData;
  onSave?: (
    v: TournamentInfoFormData
  ) => void | Promise<{ success: boolean; error?: string }>;
  onClose?: () => void;
};

export const tournamentInfoFormFields: FieldConfig[] = [
  {
    key: 'name',
    type: FieldType.TEXT,
    label: 'Tournament Name',
    required: true,
    validators: [validateTournamentName],
    info: 'Enter a clear and memorable name for your tournament',
  },
  {
    key: 'logo_url',
    type: FieldType.UPLOAD_IMAGE,
    label: 'Tournament Logo',
    required: false,
    bucketName: 'tournament-assets',
    folderPath: 'tournament_logos',
    info: 'Upload your tournament logo (JPG/PNG, automatically compressed)',
  },
];

export default function EditTournamentInfo({ value, onSave, onClose }: Props) {
  const [form, setForm] = useState<TournamentInfoFormData>({
    name: value?.name || '',
    logo_url: value?.logo_url || '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (value) {
      setForm({
        name: value.name || '',
        logo_url: value.logo_url || '',
      });
    }
  }, [value]);

  const handleChange = useCallback(
    (key: string, fieldValue: any) => {
      const newForm = { ...form, [key]: fieldValue };
      setForm(newForm);
      setErrors((prev) => ({ ...prev, [key]: '' }));
    },
    [form]
  );

  const validateForm = useCallback(() => {
    const validationErrors = validateTournamentInfoForm(form);
    setErrors(validationErrors);
    return Object.keys(validationErrors).length === 0;
  }, [form]);

  const handleSave = useCallback(async () => {
    if (!validateForm()) {
      return;
    }

    if (!onSave) {
      return;
    }

    setIsLoading(true);

    try {
      const result = await onSave(form);

      if (result?.success) {
        onClose?.();
        toast.success('Tournament info saved!');
      } else {
        toast.error(result?.error || 'Failed to save tournament info');
      }
    } catch (error) {
      toast.error('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  }, [form, onSave, validateForm, onClose]);

  const renderFormField = (field: any, index: number) => (
    <FormField
      key={`${index}-${field.key}`}
      type={field.type as FieldType}
      keyName={field.key}
      field={field}
      value={form[field.key as keyof TournamentInfoFormData]}
      error={errors[field.key]}
      onChange={handleChange}
    />
  );

  return (
    <View className="w-full flex flex-col gap-4">
      <View className="">
        <VStack space="lg" className="">
          {tournamentInfoFormFields.map(renderFormField)}
        </VStack>
      </View>

      <CTAButton
        title="Save"
        onPress={handleSave}
        loading={isLoading}
        isFormValid={!isLoading}
      />

      <View>
        <Text className="text-xs text-gray-500 font-urbanist">
          Pro tip: A clear tournament name and professional logo help
          participants easily identify your event.
        </Text>
      </View>
    </View>
  );
}
