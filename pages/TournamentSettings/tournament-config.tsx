import { EyeIcon, Trash2Icon, EditIcon } from 'lucide-react-native';
import VisibilitySetting from './VisibilitySetting';
import { FC } from 'react';
import DeleteTournament from './DeleteTournament';
import EditTournamentInfo from './EditTournamentInfo';

export type SettingConfigItem = {
  label: string;
  icon: React.ComponentType<any>;
  renderComponent: FC<{
    value?: any;
    onSave?: (v: any) => void | Promise<{ success: boolean; error?: string }>;
    onClose?: () => void;
  }>;
  path?: string;
  keyMap: string[];
};

const SETTINGS_CONFIG: SettingConfigItem[] = [
  {
    label: 'Visibility',
    icon: EyeIcon,
    renderComponent: VisibilitySetting,
    keyMap: ['public'],
  },
  {
    label: 'Edit Tournament Info',
    icon: EditIcon,
    renderComponent: EditTournamentInfo,
    keyMap: ['name', 'logo_url'],
  },
  {
    label: 'Delete Tournament',
    icon: Trash2Icon,
    renderComponent: DeleteTournament,
    keyMap: ['id'],
  },
];

export default SETTINGS_CONFIG;
