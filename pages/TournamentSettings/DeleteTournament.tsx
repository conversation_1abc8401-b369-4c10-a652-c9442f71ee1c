import { useState } from 'react';
import { View } from 'react-native';
import { ErrorText } from '@/components/ui/errortext';
import ConfirmationPrompt from '@/components/k-components/ConfirmationPrompt';
import { deleteTournament } from '@/services/tournamentService';
import { router } from 'expo-router';
import SCREENS from '@/constants/Screens';

interface DeleteTournamentProps {
  onClose?: () => void;
  value?: { id: string };
}

export default function DeleteTournament({
  value,
  onClose,
}: DeleteTournamentProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const onDelete = async () => {
    setError(null);
    if (!value?.id) return;

    setLoading(true);
    const { error, success } = await deleteTournament(value.id);
    setLoading(false);

    if (success) {
      onClose?.();
      router.push(SCREENS.MANAGE);
    } else {
      setError(error || 'Something went wrong.');
    }
  };

  return (
    <View className="flex-col w-full">
      <ConfirmationPrompt
        heading="Delete Tournament?"
        description="This action cannot be undone. Deleting this tournament will remove all associated data."
        primaryText="Delete"
        secondaryText="Cancel"
        loading={loading}
        onPrimaryPress={onDelete}
        onSecondaryPress={onClose}
        type="error"
      />
      {error && <ErrorText message={error} className="text-center mt-2" />}
    </View>
  );
}
