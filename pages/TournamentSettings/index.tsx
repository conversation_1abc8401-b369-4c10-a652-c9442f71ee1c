import React, { useState } from 'react';
import { View, ScrollView } from 'react-native';
import { Pressable } from 'react-native';
import { Text } from '@/components/ui/text';
import { Icon } from '@/components/ui/icon';
import { ChevronRightIcon, ArrowLeftIcon } from 'lucide-react-native';
import { useStatusBarColor } from '@/hooks/useStatusBarColor';
import {
  Actionsheet,
  ActionsheetBackdrop,
  ActionsheetContent,
  ActionsheetDragIndicator,
  ActionsheetDragIndicatorWrapper,
  ActionsheetScrollView,
} from '@/components/ui/actionsheet';
import SETTINGS_CONFIG from './tournament-config';
import { updateTournamentFields } from '@/services/tournamentService';
import { router } from 'expo-router';

const SettingOption = ({
  label,
  onPress,
  leftIcon,
}: {
  label: string;
  onPress: () => void;
  leftIcon: React.ReactNode;
}) => (
  <Pressable
    onPress={onPress}
    className="flex-row items-center justify-between py-3"
  >
    <View className="flex-row items-center gap-2">
      {leftIcon}
      <Text className="text-lg ml-5 font-urbanistSemiBold text-typography-800">
        {label}
      </Text>
    </View>
    <Icon as={ChevronRightIcon} size="md" className="text-typography-800" />
  </Pressable>
);

export default function TournamentSettings({ tournament }: any) {
  useStatusBarColor();
  const [activeIndex, setActiveIndex] = useState<number | null>(null);
  const [tournamentData, setTournamentData] = useState(tournament);

  const handleSettingSave = async (
    updated: Record<string, any>
  ): Promise<{ success: boolean; error?: string }> => {
    const prevData = { ...tournamentData }; // Backup for rollback
    const newData = { ...tournamentData, ...updated };

    // Optimistically update UI
    setTournamentData(newData);

    const res = await updateTournamentFields(tournamentData.id, updated);

    if (!res.success) {
      // Rollback on error
      setTournamentData(prevData);
      return { success: false, error: res.error };
    }

    return { success: true };
  };

  const extractValue = (keys: string[]) => {
    const result: Record<string, any> = {};
    keys.forEach((key) => {
      result[key] = tournamentData[key];
    });
    return result;
  };

  return (
    <>
      <ScrollView className="flex-1 bg-white">
        <View className="flex flex-col gap-1">
          <View className="bg-primary-0 h-56 flex items-start justify-center px-6">
            <View className="flex flex-row gap-3 items-center mb-5">
              <Pressable onPress={() => router.back()}>
                {/* @ts-ignore */}
                <Icon size="2xl" as={ArrowLeftIcon} className="text-white" />
              </Pressable>
              <Text className="text-3xl font-urbanistBold text-white mb-1">
                Tournament Settings
              </Text>
            </View>
            <Text className="text-base font-urbanist text-white/90 leading-relaxed">
              Manage visibility, update details, or configure match rules — it's
              your game.
            </Text>
          </View>

          <View className="px-10 pt-10 gap-6">
            {SETTINGS_CONFIG.map((item, index) => (
              <SettingOption
                key={index}
                label={item.label}
                leftIcon={
                  <Icon
                    size="xl"
                    as={item.icon}
                    className="text-typography-800"
                  />
                }
                onPress={() => setActiveIndex(index)}
              />
            ))}
          </View>
        </View>
      </ScrollView>

      <Actionsheet
        isOpen={activeIndex !== null}
        onClose={() => setActiveIndex(null)}
      >
        <ActionsheetBackdrop />
        <ActionsheetContent>
          <ActionsheetDragIndicatorWrapper>
            <ActionsheetDragIndicator />
          </ActionsheetDragIndicatorWrapper>

          <ActionsheetScrollView
            className="w-full"
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
          >
            <View className="p-3 py-6 w-full">
              {activeIndex !== null &&
                (() => {
                  const { renderComponent: Component, keyMap } =
                    SETTINGS_CONFIG[activeIndex];
                  const initialValue = extractValue(keyMap);
                  return (
                    <Component
                      value={initialValue}
                      onSave={handleSettingSave}
                      onClose={() => setActiveIndex(null)}
                    />
                  );
                })()}
            </View>
          </ActionsheetScrollView>
        </ActionsheetContent>
      </Actionsheet>
    </>
  );
}
