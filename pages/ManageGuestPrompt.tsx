import { Image } from 'react-native';
import { Text } from '@/components/ui/text';
import { Center } from '@/components/ui/center';
import { Button } from '@/components/ui/button';
import { useRouter } from 'expo-router';
import SCREENS from '@/constants/Screens';

export default function ManageGuestPrompt() {
  const router = useRouter();

  return (
    <Center className="flex-1 items-center justify-center bg-white p-6">
      {/* Illustration */}
      <Image
        source={require('@/assets/images/get-started-illustration.png')}
        resizeMode="contain"
        className="w-full max-h-80"
      />

      <Text className="text-2xl font-urbanistExtraBold text-center mt-8">
        Ready to Run the Game?
      </Text>

      <Text className="text-base font-urbanist text-center text-gray-500 mt-2 px-6">
        Login to create tournaments, manage matches, invite players, and much
        more.
      </Text>

      <Button
        size="xl"
        className="mt-8 rounded-full bg-primary-0 w-1/2"
        onPress={() => router.push(SCREENS.LOGIN)}
      >
        <Text className="font-urbanistSemiBold text-white">
          Login / Sign Up
        </Text>
      </Button>
    </Center>
  );
}
